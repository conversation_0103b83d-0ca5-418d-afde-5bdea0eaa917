//
//  HomeViewTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/7/29.
//

import XCTest
import SwiftUI
@testable import ztt2

/**
 * HomeView 单元测试
 */
final class HomeViewTests: XCTestCase {

    override func setUpWithError() throws {
        // 在每个测试方法之前调用
    }

    override func tearDownWithError() throws {
        // 在每个测试方法之后调用
    }

    /**
     * 测试 HomeView 能够正常创建
     */
    func testHomeViewCreation() throws {
        // Given
        var selectedMemberId: String?
        
        // When
        let homeView = HomeView { memberId in
            selectedMemberId = memberId
        }
        
        // Then
        XCTAssertNotNil(homeView)
    }
    
    /**
     * 测试成员选择回调
     */
    func testMemberSelectionCallback() throws {
        // Given
        var selectedMemberId: String?
        let homeView = HomeView { memberId in
            selectedMemberId = memberId
        }
        
        // When - 模拟成员选择
        let testMemberId = "test-member-id"
        homeView.onMemberSelected(testMemberId)
        
        // Then
        XCTAssertEqual(selectedMemberId, testMemberId)
    }
    
    /**
     * 测试性能
     */
    func testHomeViewPerformance() throws {
        measure {
            // 测试 HomeView 创建性能
            let homeView = HomeView { _ in }
            _ = homeView.body
        }
    }
}

/**
 * ActionButtonsView 单元测试
 */
final class ActionButtonsViewTests: XCTestCase {
    
    /**
     * 测试 ActionButtonsView 能够正常创建
     */
    func testActionButtonsViewCreation() throws {
        // Given & When
        let actionButtonsView = ActionButtonsView(
            totalScore: 100,
            dateRangeText: "本月",
            onAddMemberTapped: {},
            onFamilyOperationTapped: {},
            onTotalScoreTapped: {}
        )
        
        // Then
        XCTAssertNotNil(actionButtonsView)
    }
    
    /**
     * 测试总分显示
     */
    func testTotalScoreDisplay() throws {
        // Given
        let testScore = 861
        
        // When
        let actionButtonsView = ActionButtonsView(
            totalScore: testScore,
            dateRangeText: "本月",
            onAddMemberTapped: {},
            onFamilyOperationTapped: {},
            onTotalScoreTapped: {}
        )
        
        // Then
        XCTAssertNotNil(actionButtonsView)
        // 注意：在实际项目中，您可能需要使用 ViewInspector 或类似工具来测试 UI 内容
    }
}

/**
 * FamilyMemberGridView 单元测试
 */
final class FamilyMemberGridViewTests: XCTestCase {
    
    /**
     * 测试空状态显示
     */
    func testEmptyStateDisplay() throws {
        // Given
        let emptyMembers: [FamilyMemberGridView.FamilyMember] = []
        
        // When
        let gridView = FamilyMemberGridView(
            members: emptyMembers,
            isDeleteMode: false,
            hasFamilies: false,
            onMemberTapped: { _ in },
            onEnterDeleteMode: {},
            onExitDeleteMode: {},
            onDeleteRequested: { _ in },
            onCreateFamilyTapped: {},
            onRefresh: {}
        )
        
        // Then
        XCTAssertNotNil(gridView)
    }
    
    /**
     * 测试成员列表显示
     */
    func testMemberListDisplay() throws {
        // Given
        let testMembers = [
            FamilyMemberGridView.FamilyMember(
                id: "1",
                name: "爸爸",
                role: "father",
                currentPoints: 10
            ),
            FamilyMemberGridView.FamilyMember(
                id: "2",
                name: "妈妈",
                role: "mother",
                currentPoints: 20
            )
        ]
        
        // When
        let gridView = FamilyMemberGridView(
            members: testMembers,
            isDeleteMode: false,
            hasFamilies: true,
            onMemberTapped: { _ in },
            onEnterDeleteMode: {},
            onExitDeleteMode: {},
            onDeleteRequested: { _ in },
            onCreateFamilyTapped: {},
            onRefresh: {}
        )
        
        // Then
        XCTAssertNotNil(gridView)
    }
}

/**
 * ResponsiveLayoutConfig 单元测试
 */
final class ResponsiveLayoutConfigTests: XCTestCase {
    
    /**
     * 测试响应式断点判断
     */
    func testResponsiveBreakpoints() throws {
        // 注意：由于 GeometryProxy 是 SwiftUI 内部类型，
        // 在实际项目中您可能需要创建模拟对象或使用不同的测试策略
        
        // 测试预定义配置
        let compactConfig = ResponsiveConfig.compact
        XCTAssertEqual(compactConfig.cardWidth, 160)
        XCTAssertEqual(compactConfig.cardHeight, 140)
        
        let regularConfig = ResponsiveConfig.regular
        XCTAssertEqual(regularConfig.cardWidth, 170)
        XCTAssertEqual(regularConfig.cardHeight, 140)
        
        let largeConfig = ResponsiveConfig.large
        XCTAssertEqual(largeConfig.cardWidth, 180)
        XCTAssertEqual(largeConfig.cardHeight, 140)
        
        let extraLargeConfig = ResponsiveConfig.extraLarge
        XCTAssertEqual(extraLargeConfig.cardWidth, 200)
        XCTAssertEqual(extraLargeConfig.cardHeight, 160)
    }
    
    /**
     * 测试位置配置
     */
    func testPositionConfig() throws {
        // Given
        let positionConfig = PositionConfig(
            xRatio: 0.5,
            yRatio: 0.5,
            anchor: .center,
            offsetX: 10,
            offsetY: 20
        )
        
        // Then
        XCTAssertEqual(positionConfig.xRatio, 0.5)
        XCTAssertEqual(positionConfig.yRatio, 0.5)
        XCTAssertEqual(positionConfig.anchor, .center)
        XCTAssertEqual(positionConfig.offsetX, 10)
        XCTAssertEqual(positionConfig.offsetY, 20)
    }
}

/**
 * 积分统计功能测试
 */
final class PointsCalculationTests: XCTestCase {

    /**
     * 测试DateRangeType的displayText属性
     */
    func testDateRangeTypeDisplayText() throws {
        // Given & When & Then
        XCTAssertEqual(DateRangeType.thisWeek.displayText, "date_range.this_week".localized)
        XCTAssertEqual(DateRangeType.thisMonth.displayText, "date_range.this_month".localized)

        let customStart = Date()
        let customEnd = Calendar.current.date(byAdding: .day, value: 7, to: customStart)!
        let customRange = DateRangeType.custom(start: customStart, end: customEnd)
        XCTAssertFalse(customRange.displayText.isEmpty)
    }

    /**
     * 测试DateRangeType的dateRange属性
     */
    func testDateRangeTypeDateRange() throws {
        // Given
        let calendar = Calendar.current
        let now = Date()

        // When & Then - 测试本周
        let thisWeekRange = DateRangeType.thisWeek.dateRange
        let expectedWeekStart = calendar.dateInterval(of: .weekOfYear, for: now)?.start
        XCTAssertNotNil(expectedWeekStart)
        XCTAssertEqual(calendar.component(.weekOfYear, from: thisWeekRange.start),
                      calendar.component(.weekOfYear, from: expectedWeekStart!))

        // When & Then - 测试本月
        let thisMonthRange = DateRangeType.thisMonth.dateRange
        let expectedMonthStart = calendar.dateInterval(of: .month, for: now)?.start
        XCTAssertNotNil(expectedMonthStart)
        XCTAssertEqual(calendar.component(.month, from: thisMonthRange.start),
                      calendar.component(.month, from: expectedMonthStart!))

        // When & Then - 测试自定义范围
        let customStart = Date()
        let customEnd = calendar.date(byAdding: .day, value: 7, to: customStart)!
        let customRange = DateRangeType.custom(start: customStart, end: customEnd)
        let customDateRange = customRange.dateRange
        XCTAssertEqual(calendar.startOfDay(for: customDateRange.start),
                      calendar.startOfDay(for: customStart))
    }
}

/**
 * 中文本地化字符串测试
 * 注意：本项目仅支持简体中文，不再支持多语言功能
 */
final class LocalizationTests: XCTestCase {

    /**
     * 测试关键中文本地化字符串是否存在
     */
    func testKeyLocalizedStrings() throws {
        // 测试首页相关的中文本地化字符串
        XCTAssertFalse("home.title".localized.isEmpty)
        XCTAssertFalse("home.button.add_member".localized.isEmpty)
        XCTAssertFalse("home.button.family_operation".localized.isEmpty)
        XCTAssertFalse("home.total_score.title".localized.isEmpty)

        // 确保本地化字符串不是键本身（即已正确本地化为中文）
        XCTAssertNotEqual("home.title".localized, "home.title")
        XCTAssertNotEqual("home.button.add_member".localized, "home.button.add_member")

        // 验证返回的是中文文本
        XCTAssertEqual("home.title".localized, "转团团")
        XCTAssertEqual("home.button.add_member".localized, "添加成员")
    }

    /**
     * 测试日期范围相关的中文本地化字符串
     */
    func testDateRangeLocalizedStrings() throws {
        // 测试日期范围相关的中文本地化字符串
        XCTAssertFalse("date_range.this_week".localized.isEmpty)
        XCTAssertFalse("date_range.this_month".localized.isEmpty)
        XCTAssertFalse("date_range.custom".localized.isEmpty)

        // 确保本地化字符串不是键本身（即已正确本地化为中文）
        XCTAssertNotEqual("date_range.this_week".localized, "date_range.this_week")
        XCTAssertNotEqual("date_range.this_month".localized, "date_range.this_month")
        XCTAssertNotEqual("date_range.custom".localized, "date_range.custom")
    }
}
