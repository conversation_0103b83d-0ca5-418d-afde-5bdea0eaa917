//
//  MultiDeviceSyncTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/3.
//

import XCTest
import CoreData
import CloudKit
@testable import ztt2

/**
 * 多设备同步功能测试
 * 验证CloudKit自动同步和设置同步功能
 */
class MultiDeviceSyncTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var dataManager: DataManager!
    
    override func setUpWithError() throws {
        // 使用内存存储进行测试
        persistenceController = PersistenceController(inMemory: true)
        dataManager = DataManager.shared
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        dataManager = nil
    }
    
    // MARK: - CloudKit同步测试
    
    /**
     * 测试CloudKit容器配置
     */
    func testCloudKitContainerConfiguration() throws {
        // 验证使用的是CloudKit容器
        XCTAssertTrue(persistenceController.container is NSPersistentCloudKitContainer, 
                     "应该使用NSPersistentCloudKitContainer")
        
        // 验证CloudKit同步已启用
        XCTAssertTrue(persistenceController.isCloudKitEnabled, 
                     "CloudKit同步应该已启用")
        
        print("✅ CloudKit容器配置测试通过")
    }
    
    /**
     * 测试数据创建和同步
     */
    func testDataCreationAndSync() throws {
        let context = persistenceController.container.viewContext
        
        // 创建测试用户
        let user = User(context: context)
        user.id = UUID()
        user.nickname = "测试用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()
        
        // 创建测试订阅
        let subscription = Subscription(context: context)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user
        
        // 创建测试成员
        let member = Member(context: context)
        member.id = UUID()
        member.name = "测试成员"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user
        
        // 保存数据
        try context.save()
        
        // 验证数据已创建
        XCTAssertNotNil(user.id, "用户ID应该存在")
        XCTAssertNotNil(subscription.id, "订阅ID应该存在")
        XCTAssertNotNil(member.id, "成员ID应该存在")
        XCTAssertEqual(member.user, user, "成员应该关联到用户")
        XCTAssertEqual(subscription.user, user, "订阅应该关联到用户")
        
        print("✅ 数据创建和同步测试通过")
    }
    
    /**
     * 测试CloudKit远程变更通知处理
     */
    func testCloudKitRemoteChangeNotification() throws {
        let expectation = XCTestExpectation(description: "CloudKit远程变更通知")
        
        // 监听同步完成通知
        let observer = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil,
            queue: .main
        ) { _ in
            expectation.fulfill()
        }
        
        // 模拟远程变更通知
        NotificationCenter.default.post(
            name: .NSPersistentStoreRemoteChange,
            object: nil
        )
        
        // 等待通知处理完成
        wait(for: [expectation], timeout: 5.0)
        
        NotificationCenter.default.removeObserver(observer)
        
        print("✅ CloudKit远程变更通知处理测试通过")
    }
    
    /**
     * 测试手动触发同步
     */
    func testManualSyncTrigger() throws {
        // 触发手动同步
        persistenceController.triggerCloudKitSync()
        
        // 验证同步状态
        XCTAssertTrue(persistenceController.cloudKitSyncEnabled, "CloudKit同步应该已启用")
        
        print("✅ 手动触发同步测试通过")
    }
    
    /**
     * 测试数据一致性检查
     */
    func testDataConsistencyCheck() throws {
        let context = persistenceController.container.viewContext
        
        // 创建不完整的用户数据（缺少订阅）
        let user = User(context: context)
        user.id = UUID()
        user.nickname = "不完整用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()
        // 故意不创建订阅
        
        try context.save()
        
        // 触发数据一致性检查（通过模拟CloudKit导入）
        NotificationCenter.default.post(
            name: .NSPersistentStoreRemoteChange,
            object: nil
        )
        
        // 等待处理完成
        let expectation = XCTestExpectation(description: "数据一致性检查")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        // 验证订阅已自动创建
        context.refresh(user, mergeChanges: true)
        XCTAssertNotNil(user.subscription, "应该自动创建默认订阅")
        
        print("✅ 数据一致性检查测试通过")
    }
    
    /**
     * 测试大量数据同步性能
     */
    func testLargeDataSyncPerformance() throws {
        measure {
            let context = persistenceController.container.viewContext
            
            // 创建大量测试数据
            for i in 0..<100 {
                let member = Member(context: context)
                member.id = UUID()
                member.name = "成员\(i)"
                member.role = "son"
                member.memberNumber = Int32(i)
                member.currentPoints = Int32(i * 10)
                member.createdAt = Date()
                member.updatedAt = Date()
            }
            
            // 保存数据
            try! context.save()
        }
        
        print("✅ 大量数据同步性能测试通过")
    }
    
    /**
     * 测试iCloud同步管理器权限检查
     */
    func testICloudSyncManagerPermissions() throws {
        let syncManager = iCloudSyncManager.shared
        
        // 在没有用户的情况下，权限检查应该返回false
        XCTAssertFalse(syncManager.hasPermission(), "没有用户时应该返回false")
        
        // 创建用户后，权限检查应该返回true（因为现在所有用户都可以使用同步）
        let context = persistenceController.container.viewContext
        let user = User(context: context)
        user.id = UUID()
        user.nickname = "测试用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()
        
        try context.save()
        
        // 模拟设置当前用户
        dataManager.currentUser = user
        
        XCTAssertTrue(syncManager.hasPermission(), "有用户时应该返回true")
        
        print("✅ iCloud同步管理器权限检查测试通过")
    }
}
