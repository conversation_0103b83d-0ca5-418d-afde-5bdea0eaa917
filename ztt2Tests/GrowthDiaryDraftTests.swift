//
//  GrowthDiaryDraftTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/3.
//  成长日记草稿缓存功能测试用例
//

import XCTest
@testable import ztt2

/**
 * 成长日记草稿缓存功能测试类
 * 测试页面切换时内容的保存和恢复功能
 */
final class GrowthDiaryDraftTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // 清空UserDefaults中的测试数据
        UserDefaults.standard.removeObject(forKey: "GrowthDiaryDraft")
    }
    
    override func tearDown() {
        // 清空UserDefaults中的测试数据
        UserDefaults.standard.removeObject(forKey: "GrowthDiaryDraft")
        super.tearDown()
    }
    
    // MARK: - 草稿保存测试
    
    /**
     * 测试草稿数据保存功能
     */
    func testSaveDraftContent() {
        // 模拟草稿数据
        let draftData: [String: Any] = [
            "title": "测试标题",
            "content": "测试内容",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": "test-member-id"
        ]
        
        // 保存草稿
        UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
        
        // 验证保存成功
        let savedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(savedData, "草稿数据应该被成功保存")
        XCTAssertEqual(savedData?["title"] as? String, "测试标题")
        XCTAssertEqual(savedData?["content"] as? String, "测试内容")
        XCTAssertEqual(savedData?["selectedMemberId"] as? String, "test-member-id")
    }
    
    /**
     * 测试草稿数据加载功能
     */
    func testLoadDraftContent() {
        // 预设草稿数据
        let draftData: [String: Any] = [
            "title": "恢复的标题",
            "content": "恢复的内容",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": ""
        ]
        UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
        
        // 加载草稿数据
        let loadedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        
        // 验证加载成功
        XCTAssertNotNil(loadedData, "应该能够加载草稿数据")
        XCTAssertEqual(loadedData?["title"] as? String, "恢复的标题")
        XCTAssertEqual(loadedData?["content"] as? String, "恢复的内容")
    }
    
    /**
     * 测试草稿数据清空功能
     */
    func testClearDraftContent() {
        // 先保存一些数据
        let draftData: [String: Any] = [
            "title": "要删除的标题",
            "content": "要删除的内容"
        ]
        UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
        
        // 验证数据存在
        XCTAssertNotNil(UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft"))
        
        // 清空数据
        UserDefaults.standard.removeObject(forKey: "GrowthDiaryDraft")
        
        // 验证数据被清空
        XCTAssertNil(UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft"))
    }
    
    // MARK: - 边界情况测试
    
    /**
     * 测试空内容的草稿保存
     */
    func testSaveEmptyDraft() {
        let emptyDraft: [String: Any] = [
            "title": "",
            "content": "",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": ""
        ]
        
        UserDefaults.standard.set(emptyDraft, forKey: "GrowthDiaryDraft")
        
        let savedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(savedData, "空草稿也应该能够保存")
        XCTAssertEqual(savedData?["title"] as? String, "")
        XCTAssertEqual(savedData?["content"] as? String, "")
    }
    
    /**
     * 测试长文本内容的草稿保存
     */
    func testSaveLongContentDraft() {
        let longContent = String(repeating: "这是一段很长的测试内容。", count: 100)
        let longDraft: [String: Any] = [
            "title": "长内容测试",
            "content": longContent,
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": "test-id"
        ]
        
        UserDefaults.standard.set(longDraft, forKey: "GrowthDiaryDraft")
        
        let savedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(savedData, "长内容草稿应该能够保存")
        XCTAssertEqual(savedData?["content"] as? String, longContent)
    }
    
    /**
     * 测试特殊字符内容的草稿保存
     */
    func testSaveSpecialCharactersDraft() {
        let specialContent = "测试特殊字符：😀🎉📱💻🌟\n换行符\t制表符\"引号'单引号"
        let specialDraft: [String: Any] = [
            "title": "特殊字符测试",
            "content": specialContent,
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": ""
        ]
        
        UserDefaults.standard.set(specialDraft, forKey: "GrowthDiaryDraft")
        
        let savedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(savedData, "包含特殊字符的草稿应该能够保存")
        XCTAssertEqual(savedData?["content"] as? String, specialContent)
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试草稿保存性能
     */
    func testDraftSavePerformance() {
        let draftData: [String: Any] = [
            "title": "性能测试标题",
            "content": "性能测试内容",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": "performance-test-id"
        ]
        
        measure {
            UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
        }
    }
    
    /**
     * 测试草稿加载性能
     */
    func testDraftLoadPerformance() {
        // 预设数据
        let draftData: [String: Any] = [
            "title": "性能测试标题",
            "content": "性能测试内容",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": "performance-test-id"
        ]
        UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
        
        measure {
            _ = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        }
    }
    
    // MARK: - 集成测试
    
    /**
     * 测试完整的保存-加载-清空流程
     */
    func testCompleteDraftWorkflow() {
        // 1. 保存草稿
        let originalDraft: [String: Any] = [
            "title": "完整流程测试",
            "content": "这是一个完整的草稿流程测试",
            "timestamp": Date().timeIntervalSince1970,
            "selectedMemberId": "workflow-test-id"
        ]
        UserDefaults.standard.set(originalDraft, forKey: "GrowthDiaryDraft")
        
        // 2. 验证保存成功
        let savedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(savedData)
        XCTAssertEqual(savedData?["title"] as? String, "完整流程测试")
        
        // 3. 模拟页面切换后加载
        let loadedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNotNil(loadedData)
        XCTAssertEqual(loadedData?["content"] as? String, "这是一个完整的草稿流程测试")
        
        // 4. 清空草稿
        UserDefaults.standard.removeObject(forKey: "GrowthDiaryDraft")
        
        // 5. 验证清空成功
        let clearedData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft")
        XCTAssertNil(clearedData)
    }
}
