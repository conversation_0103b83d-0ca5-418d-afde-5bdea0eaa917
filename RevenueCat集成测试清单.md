# RevenueCat + StoreKit2 集成测试清单

## 测试前准备

### ✅ 代码集成检查
- [ ] RevenueCat SDK已添加到项目依赖
- [ ] RevenueCatManager.swift 已创建并配置
- [ ] SubscriptionService.swift 已创建并配置
- [ ] SubscriptionPermissionManager.swift 已创建
- [ ] SubscriptionSyncManager.swift 已创建
- [ ] ztt2App.swift 中已配置RevenueCat初始化
- [ ] CoreData模型已更新（User.subscriptionType, Subscription.level）

### ✅ 配置检查
- [ ] App Store Connect中已创建4个订阅产品
- [ ] RevenueCat Dashboard已配置项目和权限
- [ ] API Key已从RevenueCat获取并更新到代码中
- [ ] 产品ID在代码和App Store Connect中保持一致
- [ ] 权限ID在代码和RevenueCat Dashboard中保持一致

## 编译测试

### 1. 基础编译测试
```bash
# 在Xcode中执行
⌘ + B (Build)
```

**预期结果：**
- [ ] 项目编译成功，无错误
- [ ] 无RevenueCat相关的导入错误
- [ ] 无CoreData模型相关错误

### 2. 运行时初始化测试
```bash
# 在模拟器或真机上运行应用
⌘ + R (Run)
```

**检查控制台输出：**
- [ ] 看到 "🔧 RevenueCat配置完成" 日志
- [ ] 看到 "✅ RevenueCat已配置" 日志
- [ ] 看到 "✅ RevenueCat初始数据加载完成" 日志
- [ ] 无RevenueCat相关错误信息

## 功能测试

### 3. 订阅页面测试
- [ ] 订阅页面能正常打开
- [ ] 显示正确的价格信息（38元/月，188元/年，58元/月，388元/年）
- [ ] 初级会员和高级会员选项卡能正常切换
- [ ] 月度和年度价格选项能正常切换
- [ ] 协议勾选框功能正常

### 4. 权限检查测试
```swift
// 在代码中添加测试按钮
Button("测试权限") {
    let permissionManager = SubscriptionPermissionManager.shared
    print("AI分析权限: \(permissionManager.canUseAIAnalysis())")
    print("云同步权限: \(permissionManager.canUseCloudSync())")
    print("基础抽奖权限: \(permissionManager.canUseBasicLottery())")
    print("高级抽奖权限: \(permissionManager.canUseAdvancedLottery())")
    print("最大成员数: \(permissionManager.getMaxMembersLimit())")
}
```

**免费用户预期结果：**
- [ ] AI分析权限: false
- [ ] 云同步权限: false
- [ ] 基础抽奖权限: false
- [ ] 高级抽奖权限: false
- [ ] 最大成员数: 10

## 沙盒购买测试

### 5. 沙盒环境准备
1. **创建沙盒测试账户：**
   - [ ] 在App Store Connect中创建沙盒测试用户
   - [ ] 记录测试账户邮箱和密码

2. **设备配置：**
   - [ ] 在iOS设备的设置 → App Store中登录沙盒账户
   - [ ] 确认设备显示沙盒环境标识

### 6. 购买流程测试

#### 6.1 初级会员月度订阅测试
1. **执行购买：**
   - [ ] 在订阅页面选择"初级会员"
   - [ ] 选择"月度订阅"
   - [ ] 勾选协议
   - [ ] 点击订阅按钮

2. **验证购买流程：**
   - [ ] 弹出App Store购买确认弹窗
   - [ ] 显示正确的产品名称和价格
   - [ ] 确认购买后显示成功状态

3. **验证权限更新：**
   ```swift
   // 购买成功后检查
   let permissionManager = SubscriptionPermissionManager.shared
   print("购买后权限状态:")
   print("云同步权限: \(permissionManager.canUseCloudSync())") // 应为 true
   print("基础抽奖权限: \(permissionManager.canUseBasicLottery())") // 应为 true
   print("AI分析权限: \(permissionManager.canUseAIAnalysis())") // 应为 false
   print("最大成员数: \(permissionManager.getMaxMembersLimit())") // 应为 20
   ```

#### 6.2 高级会员年度订阅测试
1. **执行购买：**
   - [ ] 选择"高级会员"
   - [ ] 选择"年度订阅"
   - [ ] 完成购买流程

2. **验证权限更新：**
   ```swift
   // 购买成功后检查
   print("高级会员权限状态:")
   print("AI分析权限: \(permissionManager.canUseAIAnalysis())") // 应为 true
   print("高级抽奖权限: \(permissionManager.canUseAdvancedLottery())") // 应为 true
   print("最大成员数: \(permissionManager.getMaxMembersLimit())") // 应为 50
   ```

### 7. 数据同步测试
- [ ] 购买成功后，CoreData中的订阅状态正确更新
- [ ] User.subscriptionType 字段正确更新
- [ ] Subscription实体正确创建或更新
- [ ] RevenueCat Dashboard中能看到用户和订阅信息

### 8. 恢复购买测试
1. **删除并重新安装应用**
2. **执行恢复购买：**
   ```swift
   // 添加恢复购买测试按钮
   Button("恢复购买") {
       Task {
           let success = await SubscriptionService.shared.restorePurchases()
           print("恢复购买结果: \(success)")
       }
   }
   ```
3. **验证恢复结果：**
   - [ ] 恢复购买成功
   - [ ] 权限状态正确恢复
   - [ ] CoreData数据正确同步

## 错误处理测试

### 9. 网络错误测试
- [ ] 断开网络连接后尝试购买
- [ ] 验证显示正确的错误提示
- [ ] 网络恢复后功能正常

### 10. 用户取消购买测试
- [ ] 在App Store购买弹窗中点击取消
- [ ] 验证应用正确处理取消状态
- [ ] 无异常崩溃或错误状态

### 11. 产品不可用测试
- [ ] 使用错误的产品ID测试
- [ ] 验证显示"产品不可用"错误提示

## RevenueCat Dashboard验证

### 12. Dashboard数据检查
登录RevenueCat Dashboard验证：
- [ ] "Customers"页面能看到测试用户
- [ ] 用户的订阅状态正确显示
- [ ] 权限分配正确
- [ ] 购买事件正确记录

### 13. 实时数据同步
- [ ] 应用中的购买操作能实时反映到Dashboard
- [ ] Dashboard中的数据与应用中的状态一致

## 性能和稳定性测试

### 14. 内存泄漏测试
- [ ] 使用Xcode Instruments检查内存使用
- [ ] 多次购买操作后无内存泄漏
- [ ] RevenueCat相关对象正确释放

### 15. 多次购买测试
- [ ] 连续多次尝试购买同一产品
- [ ] 验证重复购买的处理逻辑
- [ ] 确保状态一致性

## 最终验证清单

### ✅ 集成完成确认
- [ ] 所有测试项目通过
- [ ] 无编译错误和警告
- [ ] 购买流程完整可用
- [ ] 权限检查逻辑正确
- [ ] 数据同步功能正常
- [ ] 错误处理完善
- [ ] RevenueCat Dashboard数据正确

### ✅ 文档和代码质量
- [ ] 代码注释完整
- [ ] 配置指南文档完整
- [ ] 测试示例代码可用
- [ ] 错误处理逻辑清晰

## 常见问题排查

### 购买失败
1. 检查产品ID是否正确
2. 确认App Store Connect中产品状态
3. 验证沙盒账户设置
4. 检查RevenueCat API Key

### 权限不生效
1. 检查RevenueCat Dashboard中的权限配置
2. 验证产品与权限的关联
3. 确认代码中的权限检查逻辑

### 数据同步问题
1. 检查CoreData模型是否正确更新
2. 验证SubscriptionSyncManager的调用
3. 确认RevenueCat delegate方法正确实现

---

**测试完成标准：**
所有测试项目通过，应用能在沙盒环境中正常完成购买、权限检查和数据同步功能。
