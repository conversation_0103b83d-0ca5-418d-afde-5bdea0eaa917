//
//  NotificationName+Extensions.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import Foundation

/**
 * 应用内通知名称扩展
 * 统一管理所有自定义通知名称
 */
extension Notification.Name {
    
    // MARK: - 成员数据更新通知
    
    /**
     * 成员积分数据变更通知
     * 当成员的积分发生变化时发送此通知
     * userInfo包含：
     * - "memberId": String - 成员ID
     * - "pointsChange": Int - 积分变化量（正数为加分，负数为扣分）
     * - "reason": String - 变更原因
     */
    static let memberPointsDidChange = Notification.Name("MemberPointsDidChange")
    
    /**
     * 成员数据批量更新通知
     * 当多个成员数据同时更新时发送此通知
     * userInfo包含：
     * - "familyId": String - 家庭ID
     * - "updateType": String - 更新类型（"points", "redemption", "lottery"等）
     */
    static let memberDataDidBatchUpdate = Notification.Name("MemberDataDidBatchUpdate")
    
    /**
     * 家庭统计需要刷新通知
     * 当家庭相关统计数据需要重新计算时发送此通知
     * userInfo包含：
     * - "familyId": String - 家庭ID
     * - "triggerSource": String - 触发来源（"member_detail", "family_operation"等）
     */
    static let familyStatisticsNeedsRefresh = Notification.Name("FamilyStatisticsNeedsRefresh")
    
    // MARK: - 订阅状态更新通知
    
    /**
     * 订阅状态变更通知
     * 当用户的订阅状态发生变化时发送此通知
     * userInfo包含：
     * - "userId": String - 用户ID
     * - "newLevel": String - 新的订阅级别 ("free", "basic", "premium")
     * - "oldLevel": String? - 旧的订阅级别 (可能为空)
     */
    static let subscriptionStatusChanged = Notification.Name("SubscriptionStatusChanged")
    
    /**
     * 订阅购买成功通知
     * 当用户成功完成订阅购买时发送此通知
     * userInfo包含：
     * - "productId": String - 产品ID
     * - "isInitialPurchase": Bool - 是否为首次购买
     */
    static let subscriptionPurchaseSucceeded = Notification.Name("SubscriptionPurchaseSucceeded")
    
    /**
     * 订阅恢复成功通知
     * 当用户成功恢复之前的订阅时发送此通知
     */
    static let subscriptionRestoreSucceeded = Notification.Name("SubscriptionRestoreSucceeded")
    
    /**
     * 订阅过期通知
     * 当用户的订阅过期时发送此通知
     * userInfo包含：
     * - "expiredLevel": String - 过期的订阅级别
     */
    static let subscriptionDidExpire = Notification.Name("SubscriptionDidExpire")
}

/**
 * 通知UserInfo键名常量
 */
struct NotificationUserInfoKey {
    static let memberId = "memberId"
    static let pointsChange = "pointsChange"
    static let reason = "reason"
    static let familyId = "familyId"
    static let updateType = "updateType"
    static let triggerSource = "triggerSource"
    
    // 订阅相关键名
    static let userId = "userId"
    static let newLevel = "newLevel"
    static let oldLevel = "oldLevel"
    static let productId = "productId"
    static let isInitialPurchase = "isInitialPurchase"
    static let expiredLevel = "expiredLevel"
}
