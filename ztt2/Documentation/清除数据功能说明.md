# 清除所有数据功能说明

## 🎯 功能概述

"清除所有数据"功能允许用户完全重置应用，删除所有存储的数据并恢复到初始状态。这是一个不可逆的操作，主要用于：

- 用户想要重新开始使用应用
- 解决数据损坏或同步问题
- 隐私保护需求
- 设备转让前的数据清理

## ⚠️ 安全措施

### 确认对话框
- 用户点击"清除所有数据"时，会显示详细的确认对话框
- 明确列出将被删除的数据类型
- 使用红色"确认清除"按钮表示危险操作
- 提供"取消"选项允许用户撤销操作

### 数据清除范围
功能会清除以下所有数据：

#### 1. Core Data数据库
- ✅ 用户信息 (User)
- ✅ 订阅信息 (Subscription)
- ✅ 全局规则 (GlobalRule)
- ✅ 家庭成员 (Member)
- ✅ 积分记录 (PointRecord)
- ✅ 成长日记 (DiaryEntry)
- ✅ AI报告 (AIReport)
- ✅ 成员规则 (MemberRule)
- ✅ 成员奖品 (MemberPrize)
- ✅ 兑换记录 (RedemptionRecord)
- ✅ 抽奖记录 (LotteryRecord)
- ✅ 抽奖配置 (LotteryConfig)
- ✅ 抽奖项目 (LotteryItem)

#### 2. Keychain敏感数据
- ✅ Apple用户ID
- ✅ 用户名和邮箱
- ✅ 登录状态
- ✅ DeepSeek API密钥

#### 3. UserDefaults用户偏好
- ✅ 登录相关设置
- ✅ CloudKit同步设置
- ✅ 成长日记草稿
- ✅ 应用语言设置
- ✅ 通知和声音设置
- ✅ 触觉反馈设置

#### 4. iCloud同步设置
- ✅ NSUbiquitousKeyValueStore中的所有设置
- ✅ 跨设备同步的用户偏好

#### 5. 缓存和临时文件
- ✅ URL缓存
- ✅ 临时目录文件
- ✅ 应用缓存目录

## 🔧 技术实现

### 核心方法结构
```swift
private func clearAllData() {
    // 1. 显示确认对话框
    showClearDataConfirmationAlert()
}

private func performDataClearance() {
    // 2. 执行数据清除
    clearCoreDataEntities()      // Core Data
    clearKeychainData()          // Keychain
    clearUserDefaults()          // UserDefaults
    clearUbiquitousKeyValueStore() // iCloud设置
    clearTemporaryFilesAndCaches() // 缓存文件
    resetApplicationState()      // 应用状态重置
}
```

### 批量删除优化
- 使用`NSBatchDeleteRequest`进行高效的批量删除
- 避免逐个删除造成的性能问题
- 正确处理Core Data关系和约束

### 状态重置
- 重置所有管理器到初始状态
- 清除观察者和订阅
- 重新初始化必要的服务

## 📱 用户体验

### 操作流程
1. 用户点击"清除所有数据"按钮
2. 显示详细的确认对话框
3. 用户确认后开始清除过程
4. 提供触觉反馈表示操作开始
5. 后台执行所有清除操作
6. 显示完成提示
7. 应用自动退出，用户需重新启动

### 反馈机制
- 🔴 触觉反馈：重型冲击反馈表示重要操作
- 📝 控制台日志：详细记录每个清除步骤
- ✅ 完成提示：确认所有数据已成功清除

## 🧪 测试覆盖

### 单元测试
- ✅ Core Data实体清除测试
- ✅ UserDefaults清除测试  
- ✅ Keychain清除测试
- ✅ 状态重置测试

### 集成测试
- ✅ 完整清除流程测试
- ✅ 数据一致性验证
- ✅ 错误处理测试

## 🔒 隐私和安全

### 数据安全
- 所有敏感数据都被安全删除
- Keychain数据使用系统安全删除
- 临时文件彻底清理

### 隐私保护
- 不保留任何用户痕迹
- 完全符合数据保护要求
- 支持GDPR"被遗忘权"

## 📋 使用建议

### 适用场景
- ✅ 用户主动要求重置
- ✅ 数据同步问题解决
- ✅ 设备转让前清理
- ✅ 隐私保护需求

### 注意事项
- ⚠️ 操作不可逆，请谨慎使用
- ⚠️ 建议用户在操作前备份重要数据
- ⚠️ 清除后需要重新登录和设置
- ⚠️ iCloud数据可能需要额外时间同步删除

## 🔄 后续改进

### 可能的增强功能
- 📊 选择性数据清除（保留某些数据）
- 💾 清除前自动备份选项
- 📤 数据导出功能
- 🔄 渐进式清除进度显示
