# 清除所有数据功能使用指南

## 🎯 功能简介

"清除所有数据"功能是ztt2应用中的一个重要安全功能，允许用户完全重置应用状态，删除所有存储的数据并恢复到初始状态。

## 📍 功能位置

该功能位于：**个人中心** → **系统设置** → **清除所有数据**

## 🔧 使用步骤

### 1. 进入个人中心
- 打开ztt2应用
- 点击底部导航栏的"个人中心"标签

### 2. 找到系统设置
- 在个人中心页面向下滚动
- 找到"系统设置"部分

### 3. 点击清除数据
- 点击"清除所有数据"选项
- 系统会立即显示确认对话框

### 4. 确认操作
- 仔细阅读确认对话框中的警告信息
- 确认要删除的数据类型包括：
  - 所有家庭成员信息
  - 积分记录和成长日记
  - 用户设置和偏好
  - 登录信息
- 点击"确认清除"按钮执行操作
- 或点击"取消"按钮撤销操作

### 5. 等待完成
- 系统会自动执行清除操作
- 完成后会显示"数据清除完成"提示
- 应用将自动退出

### 6. 重新启动
- 手动重新启动应用
- 应用将恢复到初始状态

## ⚠️ 重要警告

### 不可逆操作
- **此操作无法撤销**
- 一旦确认，所有数据将被永久删除
- 请在操作前确保已备份重要数据

### 数据清除范围
该功能将清除以下所有数据：

#### Core Data数据库
- 用户账户信息
- 所有家庭成员资料
- 积分记录和历史
- 成长日记内容
- AI分析报告
- 奖品和兑换记录
- 抽奖配置和记录

#### 用户设置
- 应用语言设置
- 通知和声音偏好
- 触觉反馈设置
- 自动同步配置

#### 登录信息
- Apple ID登录状态
- 用户认证信息
- API密钥配置

#### 缓存数据
- 临时文件
- 应用缓存
- 图片缓存

## 🔒 安全特性

### 多重确认
- 显示详细的确认对话框
- 明确列出将被删除的数据类型
- 使用红色按钮表示危险操作

### 触觉反馈
- 操作时提供重型触觉反馈
- 增强用户对操作重要性的感知

### 完整性保证
- 确保所有相关数据都被清除
- 避免残留数据造成的问题

## 🎯 适用场景

### 推荐使用情况
- ✅ 想要重新开始使用应用
- ✅ 解决数据同步问题
- ✅ 设备转让前的数据清理
- ✅ 隐私保护需求
- ✅ 应用出现异常需要重置

### 不推荐使用情况
- ❌ 仅想删除部分数据
- ❌ 临时测试或实验
- ❌ 误操作或好奇心驱动

## 🔄 操作后续

### 重新设置
清除数据后，您需要重新：
- 登录Apple账户
- 设置应用偏好
- 创建家庭成员
- 配置积分规则
- 设置奖品和抽奖

### 数据恢复
- 本地数据无法恢复
- 如果之前启用了iCloud同步，部分数据可能在重新登录后恢复
- 建议在清除前手动备份重要信息

## 🛠️ 技术细节

### 清除机制
- 使用批量删除优化性能
- 正确处理数据库关系
- 安全清除敏感信息

### 状态重置
- 重置所有管理器状态
- 清除内存中的缓存
- 恢复默认配置

## 📞 技术支持

如果在使用过程中遇到问题：
1. 确保应用已更新到最新版本
2. 重启应用后重试
3. 联系技术支持获取帮助

## 📝 版本信息

- 功能版本：v1.0
- 兼容系统：iOS 15.6+
- 最后更新：2025年8月4日

---

**注意：请谨慎使用此功能，确保在操作前已充分了解其影响。**
