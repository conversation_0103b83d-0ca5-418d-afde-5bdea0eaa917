//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import RevenueCat

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var subscriptionSyncManager = SubscriptionSyncManager.shared

    init() {
        // 初始化API密钥
        setupAPIKey()

        // 配置RevenueCat
        configureRevenueCat()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(dataManager)
                .environmentObject(authManager)
                .environmentObject(revenueCatManager)
                .environmentObject(subscriptionService)
                .environmentObject(subscriptionSyncManager)
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }

    /**
     * 配置RevenueCat
     */
    private func configureRevenueCat() {
        // ⚠️ 重要：请将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
        // 1. 登录 https://app.revenuecat.com/
        // 2. 创建新项目或选择现有项目
        // 3. 在项目设置中找到 API Keys
        // 4. 复制 Apple App Store 的 Public API Key
        // 5. 将下面的 "appl_YOUR_REVENUECAT_API_KEY_HERE" 替换为真实的API Key
        let apiKey = "appl_AoQsgjVJvNmhqMzpYtObdNflzsn"

        // 获取当前用户ID (如果已登录)
        let userId = AuthenticationManager.shared.currentUser?.appleUserID

        // 配置RevenueCat
        Task { @MainActor in
            RevenueCatManager.shared.configure(apiKey: apiKey, userId: userId)

            // 配置订阅服务
            SubscriptionService.shared.configure(userId: userId)
        }

        print("🔧 RevenueCat配置完成")
    }
}
