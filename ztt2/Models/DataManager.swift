//
//  DataManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import CoreData
import Combine

/// 数据管理器 - 统一管理所有数据操作
@MainActor
class DataManager: ObservableObject {

    static let shared = DataManager()

    let persistenceController = PersistenceController.shared
    let coreDataManager = CoreDataManager.shared
    private var cancellables = Set<AnyCancellable>()

    @Published var currentUser: User?
    @Published var members: [Member] = []

    private init() {
        // 立即初始化用户，确保用户数据可用
        setupCurrentUser()
        observeDataChanges()
        setupCloudKitSyncObserver()
    }

    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }
    
    // MARK: - 用户管理

    /// 强制刷新当前用户状态
    func refreshCurrentUser() {
        let previousUser = currentUser

        // 从AuthenticationManager获取当前用户
        if let authUser = AuthenticationManager.shared.currentUser {
            currentUser = authUser
            print("🔄 强制刷新用户状态: \(authUser.nickname ?? "未知"), 订阅类型: \(authUser.subscriptionType ?? "未知")")
            ensureUserDataIntegrity(authUser)

            // 如果用户发生变化，重新加载成员
            if previousUser != currentUser {
                loadMembers()
            }
        } else {
            // 没有登录用户，清空当前状态
            currentUser = nil
            members = []
            print("⚠️ 没有找到已登录用户，已清空数据")
        }
    }

    private func setupCurrentUser() {
        // 等待AuthenticationManager完成初始化
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.refreshCurrentUser()
        }
    }

    /// 确保用户数据完整性
    private func ensureUserDataIntegrity(_ user: User) {
        var needsSave = false

        // 确保用户有ID
        if user.id == nil {
            user.id = UUID()
            needsSave = true
            print("🔧 为用户生成UUID")
        }

        // 确保用户有创建时间
        if user.createdAt == nil {
            user.createdAt = Date()
            needsSave = true
            print("🔧 为用户设置创建时间")
        }

        // 确保用户有订阅信息
        if user.subscription == nil {
            let subscription = Subscription(context: viewContext)
            subscription.id = UUID()
            subscription.subscriptionType = "free"
            subscription.isActive = true
            subscription.createdAt = Date()
            subscription.updatedAt = Date()
            subscription.user = user
            needsSave = true
            print("🔧 为用户创建默认订阅")
        }

        if needsSave {
            save()
            print("💾 用户数据完整性修复完成")
        }
    }
    
    private func observeDataChanges() {
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.loadMembers()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 设置CloudKit同步观察者
     */
    private func setupCloudKitSyncObserver() {
        // 监听CloudKit同步完成通知
        NotificationCenter.default.publisher(for: NSNotification.Name("CloudKitSyncCompleted"))
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    print("📱 CloudKit同步完成，刷新本地数据")
                    self?.refreshCurrentUser()
                    self?.loadMembers()
                }
            }
            .store(in: &cancellables)

        print("☁️ CloudKit同步观察者已设置")
    }
    
    private func loadMembers() {
        guard let user = currentUser else { return }
        members = user.allMembers
    }

    // MARK: - 通知方法

    /**
     * 发送成员积分变更通知
     * @param member 成员对象
     * @param pointsChange 积分变化量
     * @param reason 变更原因
     */
    private func sendMemberPointsChangeNotification(member: Member, pointsChange: Int, reason: String) {
        guard let memberId = member.id?.uuidString,
              let familyId = currentUser?.id?.uuidString else {
            print("⚠️ 无法发送通知：成员或家庭ID为空")
            return
        }

        let userInfo: [String: Any] = [
            NotificationUserInfoKey.memberId: memberId,
            NotificationUserInfoKey.pointsChange: pointsChange,
            NotificationUserInfoKey.reason: reason,
            NotificationUserInfoKey.familyId: familyId,
            NotificationUserInfoKey.triggerSource: "data_manager"
        ]

        // 发送成员积分变更通知
        NotificationCenter.default.post(
            name: .memberPointsDidChange,
            object: nil,
            userInfo: userInfo
        )

        print("📊 已发送成员积分变更通知：成员\(member.displayName) 积分变化\(pointsChange)，原因：\(reason)")
    }

    /**
     * 发送家庭统计刷新通知
     * @param triggerSource 触发来源
     */
    func sendFamilyStatisticsRefreshNotification(triggerSource: String) {
        guard let familyId = currentUser?.id?.uuidString else {
            print("⚠️ 无法发送通知：家庭ID为空")
            return
        }

        let userInfo: [String: Any] = [
            NotificationUserInfoKey.familyId: familyId,
            NotificationUserInfoKey.triggerSource: triggerSource
        ]

        // 发送家庭统计刷新通知
        NotificationCenter.default.post(
            name: .familyStatisticsNeedsRefresh,
            object: nil,
            userInfo: userInfo
        )

        print("📊 已发送家庭统计刷新通知：触发源：\(triggerSource)")
    }
    
    // MARK: - 成员管理
    
    /// 创建新成员
    func createMember(name: String, role: String, birthDate: Date?, initialPoints: Int32 = 0) -> Member? {
        guard let user = currentUser else { return nil }
        
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = name
        member.role = role
        member.birthDate = birthDate
        member.memberNumber = getNextMemberNumber()
        member.currentPoints = initialPoints
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user
        
        save()
        return member
    }
    
    /// 删除成员
    func deleteMember(_ member: Member) {
        viewContext.delete(member)
        save()
    }
    
    /// 更新成员信息
    func updateMember(_ member: Member, name: String? = nil, role: String? = nil, birthDate: Date? = nil) {
        if let name = name {
            member.name = name
        }
        if let role = role {
            member.role = role
        }
        if let birthDate = birthDate {
            member.birthDate = birthDate
        }
        member.updatedAt = Date()
        save()
    }
    
    private func getNextMemberNumber() -> Int32 {
        guard let user = currentUser else { return 1 }
        let maxNumber = user.allMembers.map { $0.memberNumber }.max() ?? 0
        return maxNumber + 1
    }
    
    // MARK: - 积分管理
    
    /// 添加积分记录
    func addPointRecord(to member: Member, reason: String, value: Int32, recordType: String = "behavior") {
        let record = PointRecord(context: viewContext)
        record.id = UUID()
        record.reason = reason
        record.value = value
        record.timestamp = Date()
        record.recordType = recordType
        record.isReversed = false
        record.member = member

        // 更新成员当前积分
        member.currentPoints += value
        member.updatedAt = Date()

        save()

        // 发送成员积分变更通知
        sendMemberPointsChangeNotification(member: member, pointsChange: Int(value), reason: reason)
    }

    /// 创建积分记录（不自动更新成员积分，用于已经手动更新积分的场景）
    func createPointRecord(for member: Member, value: Int32, reason: String, recordType: String = "behavior") {
        let record = PointRecord(context: viewContext)
        record.id = UUID()
        record.reason = reason
        record.value = value
        record.timestamp = Date()
        record.recordType = recordType
        record.isReversed = false
        record.member = member

        save()
    }
    
    /// 撤销积分记录
    func reversePointRecord(_ record: PointRecord) {
        guard !record.isReversed else { return }

        record.isReversed = true

        // 恢复成员积分
        if let member = record.member {
            member.currentPoints -= record.value
            member.updatedAt = Date()
        }

        save()
    }

    /// 删除积分记录
    func deletePointRecord(_ record: PointRecord) {
        viewContext.delete(record)
        save()
    }

    /// 删除兑换记录
    func deleteRedemptionRecord(_ record: RedemptionRecord) {
        viewContext.delete(record)
        save()
    }
    
    /// 批量操作所有成员积分
    func addPointsToAllMembers(reason: String, value: Int32) {
        for member in members {
            addPointRecord(to: member, reason: reason, value: value)
        }
    }

    /// 获取成员在指定时间范围内的积分总和（只统计增加的积分，即正数记录）
    func getMemberPointsInDateRange(member: Member, startDate: Date, endDate: Date) -> Int {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        // 只统计正数积分记录（增加的积分），排除扣分记录
        request.predicate = NSPredicate(format: "member == %@ AND timestamp >= %@ AND timestamp <= %@ AND isReversed == NO AND value > 0",
                                       member, startDate as NSDate, endDate as NSDate)

        do {
            let records = try viewContext.fetch(request)
            return records.reduce(0) { $0 + Int($1.value) }
        } catch {
            print("获取积分记录失败: \(error)")
            return 0
        }
    }
    
    // MARK: - 规则管理
    
    /// 创建成员规则
    func createMemberRule(for member: Member, name: String, value: Int32, type: String, isFrequent: Bool = false) -> MemberRule {
        let rule = MemberRule(context: viewContext)
        rule.id = UUID()
        rule.name = name
        rule.value = value
        rule.type = type
        rule.isFrequent = isFrequent
        rule.createdAt = Date()
        rule.member = member
        
        save()
        return rule
    }
    
    /// 创建全局规则
    func createGlobalRule(name: String, value: Int32, type: String, isFrequent: Bool = false) -> GlobalRule? {
        guard let user = currentUser else { return nil }
        
        let rule = GlobalRule(context: viewContext)
        rule.id = UUID()
        rule.name = name
        rule.value = value
        rule.type = type
        rule.isFrequent = isFrequent
        rule.createdAt = Date()
        rule.user = user
        
        save()
        return rule
    }
    
    /// 删除成员规则
    func deleteMemberRule(_ rule: MemberRule) {
        viewContext.delete(rule)
        save()
    }

    /// 删除规则（通用方法）
    func deleteRule<T: NSManagedObject>(_ rule: T) {
        viewContext.delete(rule)
        save()
    }
    
    // MARK: - 奖品管理
    
    /// 创建成员奖品
    func createMemberPrize(for member: Member, name: String, cost: Int32, type: String? = nil) -> MemberPrize {
        let prize = MemberPrize(context: viewContext)
        prize.id = UUID()
        prize.name = name
        prize.cost = cost
        prize.type = type
        prize.createdAt = Date()
        prize.member = member
        
        save()
        return prize
    }
    
    /// 删除成员奖品
    func deleteMemberPrize(_ prize: MemberPrize) {
        viewContext.delete(prize)
        save()
    }

    /// 兑换奖品
    func redeemPrize(_ prize: MemberPrize, for member: Member) -> Bool {
        guard member.currentPoints >= prize.cost else { return false }

        // 直接扣除积分，不创建积分记录
        member.currentPoints -= prize.cost
        member.updatedAt = Date()

        // 创建兑换记录
        createRedemptionRecord(for: member, prizeName: prize.name ?? "未知奖品", cost: prize.cost)

        save()
        return true
    }

    /// 创建兑换记录
    func createRedemptionRecord(for member: Member, prizeName: String, cost: Int32, source: String = "redemption") {
        let record = RedemptionRecord(context: viewContext)
        record.id = UUID()
        record.prizeName = prizeName
        record.cost = cost
        record.timestamp = Date()
        record.source = source
        record.member = member

        save()
    }

    /// 创建抽奖记录
    func createLotteryRecord(for member: Member, toolType: String, prizeResult: String, cost: Int32) {
        let record = LotteryRecord(context: viewContext)
        record.id = UUID()
        record.toolType = toolType
        record.prizeResult = prizeResult
        record.cost = cost
        record.timestamp = Date()
        record.member = member

        save()
    }

    /// 删除抽奖记录
    func deleteLotteryRecord(_ record: LotteryRecord) {
        viewContext.delete(record)
        save()
    }

    // MARK: - 日记管理
    
    /// 创建日记条目
    func createDiaryEntry(for member: Member, title: String? = nil, content: String, timestamp: Date = Date()) -> DiaryEntry {
        let entry = DiaryEntry(context: viewContext)
        entry.id = UUID()
        entry.title = title
        entry.content = content
        entry.timestamp = timestamp
        entry.createdAt = Date()
        entry.updatedAt = Date()
        entry.member = member

        save()
        return entry
    }
    
    /// 更新日记条目
    func updateDiaryEntry(_ entry: DiaryEntry, title: String? = nil, content: String? = nil, timestamp: Date? = nil) {
        if let title = title {
            entry.title = title
        }
        if let content = content {
            entry.content = content
        }
        if let timestamp = timestamp {
            entry.timestamp = timestamp
        }
        entry.updatedAt = Date()
        save()
    }
    
    /// 删除日记条目
    func deleteDiaryEntry(_ entry: DiaryEntry) {
        viewContext.delete(entry)
        save()
    }
    
    // MARK: - AI报告管理
    
    /// 创建AI报告
    func createAIReport(for member: Member, title: String, content: String, reportType: String, inputDataSummary: String? = nil, totalRecords: Int32 = 0, positiveRecords: Int32 = 0, negativeRecords: Int32 = 0) -> AIReport {
        let report = AIReport(context: viewContext)
        report.id = UUID()
        report.title = title
        report.content = content
        report.reportType = reportType
        report.createdAt = Date()
        report.inputDataSummary = inputDataSummary
        report.totalRecords = totalRecords
        report.positiveRecords = positiveRecords
        report.negativeRecords = negativeRecords
        report.member = member
        
        save()
        return report
    }
    
    // MARK: - 订阅管理
    
    /// 更新订阅状态
    func updateSubscription(type: String, isActive: Bool, startDate: Date? = nil, endDate: Date? = nil, productIdentifier: String? = nil) {
        guard let user = currentUser else { return }
        
        if let subscription = user.subscription {
            subscription.subscriptionType = type
            subscription.isActive = isActive
            subscription.startDate = startDate
            subscription.endDate = endDate
            subscription.productIdentifier = productIdentifier
            subscription.updatedAt = Date()
        } else {
            let subscription = Subscription(context: viewContext)
            subscription.id = UUID()
            subscription.subscriptionType = type
            subscription.isActive = isActive
            subscription.startDate = startDate
            subscription.endDate = endDate
            subscription.productIdentifier = productIdentifier
            subscription.createdAt = Date()
            subscription.updatedAt = Date()
            subscription.user = user
        }
        
        save()
    }
    
    // MARK: - 抽奖管理

    /// 创建抽奖配置
    func createLotteryConfig(for member: Member, toolType: String, itemCount: Int32, costPerPlay: Int32) -> LotteryConfig {
        let config = LotteryConfig(context: viewContext)
        config.id = UUID()
        config.toolType = toolType
        config.itemCount = itemCount
        config.costPerPlay = costPerPlay
        config.createdAt = Date()
        config.updatedAt = Date()
        config.member = member

        save()
        return config
    }

    /// 为抽奖配置添加奖品项目
    func addLotteryItem(to config: LotteryConfig, itemIndex: Int32, prizeName: String) -> LotteryItem {
        let item = LotteryItem(context: viewContext)
        item.id = UUID()
        item.itemIndex = itemIndex
        item.prizeName = prizeName
        item.createdAt = Date()
        item.lotteryConfig = config

        save()
        return item
    }

    // MARK: - 大转盘配置管理

    /// 保存大转盘配置
    /// - Parameters:
    ///   - member: 目标成员
    ///   - sectorCount: 分区数量
    ///   - costPerPlay: 每次消耗积分
    ///   - sectorPrizes: 分区奖品数组
    /// - Returns: 保存的配置对象
    @discardableResult
    func saveWheelConfig(
        for member: Member,
        sectorCount: Int,
        costPerPlay: Int,
        sectorPrizes: [String]
    ) -> LotteryConfig? {
        // 验证输入数据
        guard validateWheelConfigData(
            sectorCount: sectorCount,
            costPerPlay: costPerPlay,
            sectorPrizes: sectorPrizes
        ) else {
            print("❌ 大转盘配置数据验证失败")
            return nil
        }

        // 使用Member扩展方法创建或更新配置
        let config = member.createOrUpdateLotteryConfig(
            toolType: .wheel,
            itemCount: sectorCount,
            costPerPlay: costPerPlay,
            prizeNames: sectorPrizes,
            in: viewContext
        )

        // 保存到Core Data
        save()

        print("✅ 大转盘配置保存成功: 成员=\(member.displayName), 分区数=\(sectorCount), 积分=\(costPerPlay)")
        return config
    }

    /// 获取成员的大转盘配置
    /// - Parameter member: 目标成员
    /// - Returns: 大转盘配置对象，如果不存在则返回nil
    func getWheelConfig(for member: Member) -> LotteryConfig? {
        return member.getLotteryConfig(for: .wheel)
    }

    // MARK: - 盲盒配置管理

    /// 保存盲盒配置
    /// - Parameters:
    ///   - member: 目标成员
    ///   - boxCount: 盲盒数量
    ///   - costPerPlay: 每次消耗积分
    ///   - boxPrizes: 盲盒奖品数组
    /// - Returns: 保存的配置对象
    @discardableResult
    func saveBlindBoxConfig(
        for member: Member,
        boxCount: Int,
        costPerPlay: Int,
        boxPrizes: [String]
    ) -> LotteryConfig? {
        // 验证输入数据
        guard validateBlindBoxConfigData(
            boxCount: boxCount,
            costPerPlay: costPerPlay,
            boxPrizes: boxPrizes
        ) else {
            print("❌ 盲盒配置数据验证失败")
            return nil
        }

        // 使用Member扩展方法创建或更新配置
        let config = member.createOrUpdateLotteryConfig(
            toolType: .blindbox,
            itemCount: boxCount,
            costPerPlay: costPerPlay,
            prizeNames: boxPrizes,
            in: viewContext
        )

        // 保存到Core Data
        save()

        print("✅ 盲盒配置保存成功: 成员=\(member.displayName), 盲盒数=\(boxCount), 积分=\(costPerPlay)")
        return config
    }

    /// 获取成员的盲盒配置
    /// - Parameter member: 目标成员
    /// - Returns: 盲盒配置对象，如果不存在则返回nil
    func getBlindBoxConfig(for member: Member) -> LotteryConfig? {
        return member.getLotteryConfig(for: .blindbox)
    }

    /// 删除成员的大转盘配置
    /// - Parameter member: 目标成员
    /// - Returns: 删除是否成功
    @discardableResult
    func deleteWheelConfig(for member: Member) -> Bool {
        guard let config = getWheelConfig(for: member) else {
            print("⚠️ 未找到要删除的大转盘配置")
            return false
        }

        // 删除配置（关联的LotteryItem会自动级联删除）
        viewContext.delete(config)
        save()

        print("✅ 大转盘配置删除成功: 成员=\(member.displayName)")
        return true
    }

    // MARK: - 刮刮卡配置管理

    /// 保存刮刮卡配置
    /// - Parameters:
    ///   - member: 目标成员
    ///   - cardCount: 刮刮卡数量
    ///   - costPerPlay: 每次消耗积分
    ///   - cardPrizes: 刮刮卡奖品数组
    /// - Returns: 保存的配置对象
    @discardableResult
    func saveScratchCardConfig(
        for member: Member,
        cardCount: Int,
        costPerPlay: Int,
        cardPrizes: [String]
    ) -> LotteryConfig? {
        // 验证输入数据
        guard validateScratchCardConfigData(
            cardCount: cardCount,
            costPerPlay: costPerPlay,
            cardPrizes: cardPrizes
        ) else {
            print("❌ 刮刮卡配置数据验证失败")
            return nil
        }

        // 使用Member扩展方法创建或更新配置
        let config = member.createOrUpdateLotteryConfig(
            toolType: .scratchcard,
            itemCount: cardCount,
            costPerPlay: costPerPlay,
            prizeNames: cardPrizes,
            in: viewContext
        )

        // 保存到Core Data
        save()

        print("✅ 刮刮卡配置保存成功: 成员=\(member.displayName), 刮刮卡数=\(cardCount), 积分=\(costPerPlay)")
        return config
    }

    /// 获取成员的刮刮卡配置
    /// - Parameter member: 目标成员
    /// - Returns: 刮刮卡配置对象，如果不存在则返回nil
    func getScratchCardConfig(for member: Member) -> LotteryConfig? {
        return member.getLotteryConfig(for: .scratchcard)
    }

    /// 删除成员的刮刮卡配置
    /// - Parameter member: 目标成员
    /// - Returns: 删除是否成功
    @discardableResult
    func deleteScratchCardConfig(for member: Member) -> Bool {
        guard let config = getScratchCardConfig(for: member) else {
            print("⚠️ 未找到要删除的刮刮卡配置")
            return false
        }

        // 删除配置（关联的LotteryItem会自动级联删除）
        viewContext.delete(config)
        save()

        print("✅ 刮刮卡配置删除成功: 成员=\(member.displayName)")
        return true
    }

    /// 验证大转盘配置数据
    /// - Parameters:
    ///   - sectorCount: 分区数量
    ///   - costPerPlay: 每次消耗积分
    ///   - sectorPrizes: 分区奖品数组
    /// - Returns: 验证是否通过
    private func validateWheelConfigData(
        sectorCount: Int,
        costPerPlay: Int,
        sectorPrizes: [String]
    ) -> Bool {
        let toolType = LotteryConfig.ToolType.wheel

        // 验证分区数量范围
        guard sectorCount >= toolType.minItemCount && sectorCount <= toolType.maxItemCount else {
            print("❌ 分区数量超出范围: \(sectorCount), 有效范围: \(toolType.minItemCount)-\(toolType.maxItemCount)")
            return false
        }

        // 验证积分设置
        guard costPerPlay >= 0 else {
            print("❌ 每次消耗积分不能为负数: \(costPerPlay)")
            return false
        }

        // 验证奖品数组长度
        guard sectorPrizes.count == sectorCount else {
            print("❌ 奖品数量与分区数量不匹配: 奖品=\(sectorPrizes.count), 分区=\(sectorCount)")
            return false
        }

        // 验证奖品名称
        for (index, prize) in sectorPrizes.enumerated() {
            let trimmedPrize = prize.trimmingCharacters(in: .whitespacesAndNewlines)

            // 检查是否为空
            guard !trimmedPrize.isEmpty else {
                print("❌ 分区 \(index + 1) 的奖品名称不能为空")
                return false
            }

            // 检查长度
            guard trimmedPrize.count <= 20 else {
                print("❌ 分区 \(index + 1) 的奖品名称过长: \(trimmedPrize.count) 字符，最多20字符")
                return false
            }
        }

        return true
    }

    /// 验证盲盒配置数据
    /// - Parameters:
    ///   - boxCount: 盲盒数量
    ///   - costPerPlay: 每次消耗积分
    ///   - boxPrizes: 盲盒奖品数组
    /// - Returns: 验证是否通过
    private func validateBlindBoxConfigData(
        boxCount: Int,
        costPerPlay: Int,
        boxPrizes: [String]
    ) -> Bool {
        let toolType = LotteryConfig.ToolType.blindbox

        // 验证盲盒数量范围
        guard boxCount >= toolType.minItemCount && boxCount <= toolType.maxItemCount else {
            print("❌ 盲盒数量超出范围: \(boxCount), 有效范围: \(toolType.minItemCount)-\(toolType.maxItemCount)")
            return false
        }

        // 验证积分设置
        guard costPerPlay >= 0 else {
            print("❌ 每次消耗积分不能为负数: \(costPerPlay)")
            return false
        }

        // 验证奖品数组长度
        guard boxPrizes.count == boxCount else {
            print("❌ 奖品数量与盲盒数量不匹配: 奖品=\(boxPrizes.count), 盲盒=\(boxCount)")
            return false
        }

        // 验证奖品名称
        for (index, prize) in boxPrizes.enumerated() {
            let trimmedPrize = prize.trimmingCharacters(in: .whitespacesAndNewlines)

            // 检查是否为空
            guard !trimmedPrize.isEmpty else {
                print("❌ 盲盒 \(index + 1) 的奖品名称不能为空")
                return false
            }

            // 检查长度
            guard trimmedPrize.count <= 20 else {
                print("❌ 盲盒 \(index + 1) 的奖品名称过长: \(trimmedPrize.count) 字符，最多20字符")
                return false
            }
        }

        return true
    }

    /// 验证刮刮卡配置数据
    /// - Parameters:
    ///   - cardCount: 刮刮卡数量
    ///   - costPerPlay: 每次消耗积分
    ///   - cardPrizes: 刮刮卡奖品数组
    /// - Returns: 验证是否通过
    private func validateScratchCardConfigData(
        cardCount: Int,
        costPerPlay: Int,
        cardPrizes: [String]
    ) -> Bool {
        let toolType = LotteryConfig.ToolType.scratchcard

        // 验证刮刮卡数量范围
        guard cardCount >= toolType.minItemCount && cardCount <= toolType.maxItemCount else {
            print("❌ 刮刮卡数量超出范围: \(cardCount), 有效范围: \(toolType.minItemCount)-\(toolType.maxItemCount)")
            return false
        }

        // 验证积分设置
        guard costPerPlay >= 0 else {
            print("❌ 每次消耗积分不能为负数: \(costPerPlay)")
            return false
        }

        // 验证奖品数组长度
        guard cardPrizes.count == cardCount else {
            print("❌ 奖品数量与刮刮卡数量不匹配: 奖品=\(cardPrizes.count), 刮刮卡=\(cardCount)")
            return false
        }

        // 验证奖品名称
        for (index, prize) in cardPrizes.enumerated() {
            let trimmedPrize = prize.trimmingCharacters(in: .whitespacesAndNewlines)

            // 检查是否为空
            guard !trimmedPrize.isEmpty else {
                print("❌ 刮刮卡 \(index + 1) 的奖品名称不能为空")
                return false
            }

            // 检查长度
            guard trimmedPrize.count <= 20 else {
                print("❌ 刮刮卡 \(index + 1) 的奖品名称过长: \(trimmedPrize.count) 字符，最多20字符")
                return false
            }
        }

        return true
    }

    /// 执行抽奖
    func performLottery(config: LotteryConfig, for member: Member) -> (success: Bool, prizeResult: String?) {
        guard member.currentPoints >= config.costPerPlay else {
            return (false, nil)
        }

        // 随机选择奖品
        let items = config.allItems
        guard !items.isEmpty else { return (false, nil) }

        let randomIndex = Int.random(in: 0..<items.count)
        let selectedItem = items[randomIndex]

        // 直接扣除积分，不创建积分记录
        member.currentPoints -= config.costPerPlay
        member.updatedAt = Date()

        // 创建抽奖记录（直接显示在兑换记录中）
        let record = LotteryRecord(context: viewContext)
        record.id = UUID()
        record.toolType = config.toolType
        record.prizeResult = selectedItem.prizeName
        record.cost = config.costPerPlay
        record.timestamp = Date()
        record.member = member

        save()
        return (true, selectedItem.prizeName)
    }

    /// 删除抽奖配置
    func deleteLotteryConfig(_ config: LotteryConfig) {
        viewContext.delete(config)
        save()
    }

    // MARK: - 统计分析

    /// 获取成员的积分统计
    func getPointStatistics(for member: Member, in dateRange: DateInterval? = nil) -> (total: Int32, positive: Int32, negative: Int32) {
        let records = member.behaviorPointRecords

        let filteredRecords: [PointRecord]
        if let dateRange = dateRange {
            filteredRecords = records.filter {
                guard let timestamp = $0.timestamp else { return false }
                return dateRange.contains(timestamp)
            }
        } else {
            filteredRecords = records
        }

        let total = filteredRecords.reduce(0) { $0 + $1.value }
        let positive = filteredRecords.filter { $0.value > 0 }.reduce(0) { $0 + $1.value }
        let negative = filteredRecords.filter { $0.value < 0 }.reduce(0) { $0 + $1.value }

        return (total, positive, abs(negative))
    }

    /// 获取全家积分统计
    func getFamilyPointStatistics(in dateRange: DateInterval? = nil) -> (total: Int32, positive: Int32, negative: Int32) {
        var totalPoints: Int32 = 0
        var positivePoints: Int32 = 0
        var negativePoints: Int32 = 0

        for member in members {
            let stats = getPointStatistics(for: member, in: dateRange)
            totalPoints += stats.total
            positivePoints += stats.positive
            negativePoints += stats.negative
        }

        return (totalPoints, positivePoints, negativePoints)
    }

    /// 检查成员是否可以使用特定功能
    func canMemberUseFeature(_ member: Member, feature: String) -> Bool {
        guard let user = currentUser else { return false }

        switch feature {
        case "wheel": // 大转盘
            return user.isBasicMemberOrAbove
        case "blindbox", "scratchcard": // 盲盒、刮刮卡
            return user.isPremiumMember
        case "ai_analysis": // AI分析
            return user.isPremiumMember && (member.canGenerateAnalysisReport || member.canGenerateGrowthReport)
        default:
            return true
        }
    }

    // MARK: - 数据清理

    /// 清理过期数据（可选功能）
    func cleanupExpiredData() {
        // 清理超过1年的积分记录（保留重要记录）
        let oneYearAgo = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()

        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.predicate = NSPredicate(format: "timestamp < %@ AND recordType == %@", oneYearAgo as NSDate, "behavior")

        do {
            let oldRecords = try viewContext.fetch(request)
            for record in oldRecords {
                viewContext.delete(record)
            }
            save()
        } catch {
            print("Failed to cleanup expired data: \(error)")
        }
    }

    // MARK: - 数据保存

    func save() {
        persistenceController.save()

        // 保存后刷新数据
        DispatchQueue.main.async {
            self.loadMembers()
        }
    }

    func saveContext() {
        save()
    }

    // MARK: - Reset Methods

    /**
     * 重置DataManager到初始状态
     * 用于清除所有数据后的状态重置
     */
    func resetToInitialState() {
        print("🔄 重置DataManager到初始状态...")

        // 清空当前数据
        currentUser = nil
        members = []

        // 取消所有观察者
        cancellables.removeAll()

        // 重新初始化
        setupCurrentUser()
        observeDataChanges()
        setupCloudKitSyncObserver()

        print("✅ DataManager重置完成")
    }


}
