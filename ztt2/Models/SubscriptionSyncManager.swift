//
//  SubscriptionSyncManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import Foundation
import CoreData
import Combine

/**
 * 订阅状态同步管理器
 * 负责在RevenueCat和CoreData之间同步订阅状态
 * 确保数据一致性和权限正确管理
 */
@MainActor
class SubscriptionSyncManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = SubscriptionSyncManager()
    
    // MARK: - Properties
    private let dataManager = DataManager.shared
    private let revenueCatManager = RevenueCatManager.shared
    private let subscriptionService = SubscriptionService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /**
     * 手动同步订阅状态
     */
    func syncSubscriptionStatus() {
        guard let user = dataManager.currentUser else {
            print("⚠️ 无法同步订阅状态：当前用户为空")
            return
        }
        
        let currentLevel = revenueCatManager.currentSubscriptionLevel
        let expirationDate = revenueCatManager.expirationDate
        
        // 更新用户的订阅类型
        user.subscriptionType = currentLevel.rawValue
        
        // 更新或创建订阅记录
        updateOrCreateSubscription(for: user, level: currentLevel, expirationDate: expirationDate)
        
        // 保存更改
        dataManager.saveContext()
        
        print("✅ 订阅状态同步完成: \(currentLevel.displayName)")
    }
    
    /**
     * 检查并修复订阅状态不一致
     */
    func validateAndFixSubscriptionConsistency() {
        guard let user = dataManager.currentUser else { return }
        
        let revenueCatLevel = revenueCatManager.currentSubscriptionLevel.rawValue
        let userSubscriptionType = user.subscriptionType ?? "free"
        let subscriptionLevel = user.subscription?.level ?? "free"
        
        // 检查是否存在不一致
        if revenueCatLevel != userSubscriptionType || revenueCatLevel != subscriptionLevel {
            print("⚠️ 检测到订阅状态不一致，正在修复...")
            print("   RevenueCat: \(revenueCatLevel)")
            print("   User.subscriptionType: \(userSubscriptionType)")
            print("   Subscription.level: \(subscriptionLevel)")
            
            // 以RevenueCat的状态为准进行修复
            syncSubscriptionStatus()
        }
    }
    
    /**
     * 处理订阅等级变化
     */
    func handleSubscriptionLevelChange(from oldLevel: String, to newLevel: String) {
        print("📱 处理订阅等级变化: \(oldLevel) -> \(newLevel)")
        
        // 发送订阅状态变化通知
        NotificationCenter.default.post(
            name: NSNotification.Name("SubscriptionStatusChanged"),
            object: nil,
            userInfo: [
                "oldLevel": oldLevel,
                "newLevel": newLevel
            ]
        )
        
        // 处理特定的升级/降级逻辑
        if oldLevel == "free" && newLevel != "free" {
            handleUpgradeFromFree(to: newLevel)
        } else if oldLevel != "free" && newLevel == "free" {
            handleDowngradeToFree(from: oldLevel)
        } else if oldLevel != newLevel {
            handleLevelChange(from: oldLevel, to: newLevel)
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听RevenueCat订阅状态变化
        revenueCatManager.$currentSubscriptionLevel
            .sink { [weak self] newLevel in
                self?.handleRevenueCatLevelChange(newLevel)
            }
            .store(in: &cancellables)
        
        // 监听订阅购买成功通知
        NotificationCenter.default.publisher(for: .subscriptionPurchaseSuccess)
            .sink { [weak self] _ in
                self?.syncSubscriptionStatus()
            }
            .store(in: &cancellables)
    }
    
    /**
     * 处理RevenueCat订阅等级变化
     */
    private func handleRevenueCatLevelChange(_ newLevel: RevenueCatManager.SubscriptionLevel) {
        guard let user = dataManager.currentUser else { return }
        
        let oldLevel = user.subscriptionType ?? "free"
        let newLevelString = newLevel.rawValue
        
        if oldLevel != newLevelString {
            // 更新用户订阅状态
            user.subscriptionType = newLevelString
            
            // 更新订阅记录
            updateOrCreateSubscription(for: user, level: newLevel, expirationDate: revenueCatManager.expirationDate)
            
            // 保存更改
            dataManager.saveContext()
            
            // 处理等级变化
            handleSubscriptionLevelChange(from: oldLevel, to: newLevelString)
        }
    }
    
    /**
     * 更新或创建订阅记录
     */
    private func updateOrCreateSubscription(for user: User, level: RevenueCatManager.SubscriptionLevel, expirationDate: Date?) {
        if let subscription = user.subscription {
            // 更新现有订阅
            subscription.level = level.rawValue
            subscription.subscriptionType = level.rawValue
            subscription.endDate = expirationDate
            subscription.isActive = level != .free
            subscription.updatedAt = Date()
        } else {
            // 创建新订阅
            let newSubscription = Subscription(context: dataManager.viewContext)
            newSubscription.id = UUID()
            newSubscription.level = level.rawValue
            newSubscription.subscriptionType = level.rawValue
            newSubscription.startDate = Date()
            newSubscription.endDate = expirationDate
            newSubscription.isActive = level != .free
            newSubscription.createdAt = Date()
            newSubscription.updatedAt = Date()
            newSubscription.user = user
        }
    }
    
    /**
     * 处理从免费升级
     */
    private func handleUpgradeFromFree(to newLevel: String) {
        print("🎉 用户从免费升级到: \(newLevel)")
        
        // 启用CloudKit同步（如果是付费用户）
        if newLevel != "free" {
            NotificationCenter.default.post(name: NSNotification.Name("EnableCloudKitSync"), object: nil)
        }
        
        // 发送升级成功通知
        NotificationCenter.default.post(
            name: NSNotification.Name("SubscriptionUpgradeSuccess"),
            object: nil,
            userInfo: ["newLevel": newLevel]
        )
    }
    
    /**
     * 处理降级到免费
     */
    private func handleDowngradeToFree(from oldLevel: String) {
        print("📉 用户从\(oldLevel)降级到免费")
        
        // 禁用CloudKit同步
        NotificationCenter.default.post(name: NSNotification.Name("DisableCloudKitSync"), object: nil)
        
        // 发送降级通知
        NotificationCenter.default.post(
            name: NSNotification.Name("SubscriptionDowngradeToFree"),
            object: nil,
            userInfo: ["oldLevel": oldLevel]
        )
    }
    
    /**
     * 处理等级变化（非免费之间的变化）
     */
    private func handleLevelChange(from oldLevel: String, to newLevel: String) {
        print("🔄 订阅等级变化: \(oldLevel) -> \(newLevel)")
        
        // 发送等级变化通知
        NotificationCenter.default.post(
            name: NSNotification.Name("SubscriptionLevelChanged"),
            object: nil,
            userInfo: [
                "oldLevel": oldLevel,
                "newLevel": newLevel
            ]
        )
    }
    
    /**
     * 获取订阅状态摘要
     */
    func getSubscriptionStatusSummary() -> [String: Any] {
        guard let user = dataManager.currentUser else {
            return ["error": "No current user"]
        }
        
        let revenueCatLevel = revenueCatManager.currentSubscriptionLevel.rawValue
        let userSubscriptionType = user.subscriptionType ?? "free"
        let subscriptionLevel = user.subscription?.level ?? "free"
        let isActive = user.subscription?.isActive ?? false
        let expirationDate = user.subscription?.endDate
        
        return [
            "revenueCatLevel": revenueCatLevel,
            "userSubscriptionType": userSubscriptionType,
            "subscriptionLevel": subscriptionLevel,
            "isActive": isActive,
            "expirationDate": expirationDate?.description ?? "nil",
            "isConsistent": revenueCatLevel == userSubscriptionType && revenueCatLevel == subscriptionLevel
        ]
    }
}

// MARK: - Debug Helper

extension SubscriptionSyncManager {
    /**
     * 打印订阅状态调试信息
     */
    func printDebugInfo() {
        let summary = getSubscriptionStatusSummary()
        print("📊 订阅状态调试信息:")
        for (key, value) in summary {
            print("   \(key): \(value)")
        }
    }
}
