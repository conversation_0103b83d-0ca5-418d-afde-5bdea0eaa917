//
//  DataModelUsageExamples.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import SwiftUI

/// 数据模型使用示例
/// 这个文件展示了如何使用 DataManager 和 Core Data 实体
/// 注意：这是示例代码，实际使用时请根据具体需求调整
@MainActor
struct DataModelUsageExamples {

    private let dataManager = DataManager.shared

    // MARK: - 用户和成员管理示例

    /// 创建家庭成员示例
    func createFamilyMembersExample() {
        // 创建爸爸
        let _ = dataManager.createMember(
            name: "张爸爸",
            role: "father",
            birthDate: Calendar.current.date(byAdding: .year, value: -35, to: Date()),
            initialPoints: 0
        )

        // 创建妈妈
        let _ = dataManager.createMember(
            name: "李妈妈",
            role: "mother",
            birthDate: Calendar.current.date(byAdding: .year, value: -32, to: Date()),
            initialPoints: 0
        )

        // 创建儿子
        let _ = dataManager.createMember(
            name: "张小明",
            role: "son",
            birthDate: Calendar.current.date(byAdding: .year, value: -8, to: Date()),
            initialPoints: 100
        )

        // 创建女儿
        let _ = dataManager.createMember(
            name: "张小红",
            role: "daughter",
            birthDate: Calendar.current.date(byAdding: .year, value: -6, to: Date()),
            initialPoints: 80
        )
        
        print("创建了 \(dataManager.members.count) 个家庭成员")
    }
    
    // MARK: - 积分系统示例
    
    /// 积分操作示例
    func pointSystemExample() {
        guard let member = dataManager.members.first else { return }
        
        // 添加正面行为积分
        dataManager.addPointRecord(
            to: member,
            reason: "按时完成作业",
            value: 10
        )
        
        dataManager.addPointRecord(
            to: member,
            reason: "主动帮助家务",
            value: 15
        )
        
        // 扣除负面行为积分
        dataManager.addPointRecord(
            to: member,
            reason: "忘记收拾玩具",
            value: -5
        )
        
        // 批量给所有成员加分
        dataManager.addPointsToAllMembers(
            reason: "全家一起大扫除",
            value: 20
        )
        
        print("\(member.name ?? "未知成员") 当前积分: \(member.currentPoints)")
    }
    
    // MARK: - 规则管理示例
    
    /// 创建规则示例
    func createRulesExample() {
        guard let member = dataManager.members.first else { return }
        
        // 创建成员专属规则
        let _ = dataManager.createMemberRule(
            for: member,
            name: "完成作业",
            value: 10,
            type: "add",
            isFrequent: true
        )
        
        let _ = dataManager.createMemberRule(
            for: member,
            name: "迟到",
            value: -5,
            type: "deduct",
            isFrequent: true
        )
        
        // 创建全局规则（适用于所有成员）
        let _ = dataManager.createGlobalRule(
            name: "参与家庭活动",
            value: 15,
            type: "add",
            isFrequent: true
        )
        
        let _ = dataManager.createGlobalRule(
            name: "不听话",
            value: -10,
            type: "deduct",
            isFrequent: false
        )
        
        print("创建了 \(member.frequentMemberRules.count) 个常用成员规则")
    }
    
    // MARK: - 奖品系统示例
    
    /// 奖品管理示例
    func prizeSystemExample() {
        guard let member = dataManager.members.first else { return }
        
        // 创建奖品
        let toy = dataManager.createMemberPrize(
            for: member,
            name: "新玩具",
            cost: 50,
            type: "toy"
        )
        
        let _ = dataManager.createMemberPrize(
            for: member,
            name: "看电影",
            cost: 30,
            type: "activity"
        )
        
        let _ = dataManager.createMemberPrize(
            for: member,
            name: "零花钱10元",
            cost: 100,
            type: "money"
        )
        
        // 兑换奖品
        let success = dataManager.redeemPrize(toy, for: member)
        if success {
            print("\(member.name ?? "未知成员") 成功兑换了 \(toy.name ?? "未知奖品")")
        } else {
            print("积分不足，无法兑换")
        }
    }
    
    // MARK: - 抽奖系统示例
    
    /// 抽奖系统示例
    func lotterySystemExample() {
        guard let member = dataManager.members.first else { return }
        
        // 创建大转盘配置
        let wheelConfig = dataManager.createLotteryConfig(
            for: member,
            toolType: "wheel",
            itemCount: 8,
            costPerPlay: 20
        )
        
        // 添加奖品项目
        let prizes = ["小贴纸", "铅笔", "橡皮", "尺子", "谢谢参与", "小玩具", "糖果", "书签"]
        for (index, prize) in prizes.enumerated() {
            let _ = dataManager.addLotteryItem(
                to: wheelConfig,
                itemIndex: Int32(index),
                prizeName: prize
            )
        }
        
        // 执行抽奖
        let result = dataManager.performLottery(config: wheelConfig, for: member)
        if result.success {
            print("\(member.name ?? "未知成员") 抽中了: \(result.prizeResult ?? "未知奖品")")
        } else {
            print("积分不足，无法抽奖")
        }
    }
    
    // MARK: - 日记系统示例
    
    /// 成长日记示例
    func diarySystemExample() {
        guard let child = dataManager.members.first(where: { $0.isChild }) else { return }
        
        // 创建日记条目
        let _ = dataManager.createDiaryEntry(
            for: child,
            content: "今天学会了骑自行车，虽然摔了几次，但是最后成功了！妈妈说我很勇敢。",
            timestamp: Date()
        )
        
        let _ = dataManager.createDiaryEntry(
            for: child,
            content: "和同学一起做了科学实验，观察了植物的生长过程，很有趣！",
            timestamp: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        )
        
        let _ = dataManager.createDiaryEntry(
            for: child,
            content: "今天帮妈妈做饭，学会了煎蛋，虽然有点糊了，但是很好吃！",
            timestamp: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date()
        )
        
        print("\(child.name ?? "未知孩子") 已有 \(child.allDiaryEntries.count) 篇日记")
    }
    
    // MARK: - AI报告示例
    
    /// AI报告创建示例
    func aiReportExample() {
        guard let child = dataManager.members.first(where: { $0.isChild }) else { return }
        
        // 检查是否可以生成报告
        if child.canGenerateAnalysisReport {
            let _ = dataManager.createAIReport(
                for: child,
                title: "行为分析报告 - \(child.name ?? "未知孩子")",
                content: "根据最近的积分记录分析，\(child.name ?? "该孩子") 在学习方面表现积极，建议继续鼓励...",
                reportType: "analysis",
                inputDataSummary: "分析了最近30天的积分记录",
                totalRecords: Int32(child.behaviorPointRecords.count),
                positiveRecords: Int32(child.behaviorPointRecords.filter { $0.value > 0 }.count),
                negativeRecords: Int32(child.behaviorPointRecords.filter { $0.value < 0 }.count)
            )
        }
        
        if child.canGenerateGrowthReport {
            let _ = dataManager.createAIReport(
                for: child,
                title: "成长分析报告 - \(child.name ?? "未知孩子")",
                content: "通过分析日记内容，发现\(child.name ?? "该孩子") 在社交能力和学习兴趣方面都有显著提升...",
                reportType: "growth",
                inputDataSummary: "分析了最近的日记内容",
                totalRecords: Int32(child.allDiaryEntries.count)
            )
        }

        print("\(child.name ?? "未知孩子") 已有 \(child.allAIReports.count) 份AI报告")
    }
    
    // MARK: - 订阅管理示例
    
    /// 订阅管理示例
    func subscriptionExample() {
        // 升级到高级会员
        dataManager.updateSubscription(
            type: "premium",
            isActive: true,
            startDate: Date(),
            endDate: Calendar.current.date(byAdding: .year, value: 1, to: Date()),
            productIdentifier: "com.ztt.premium.yearly"
        )
        
        // 检查功能权限
        if let member = dataManager.members.first {
            let canUseAI = dataManager.canMemberUseFeature(member, feature: "ai_analysis")
            let canUseLottery = dataManager.canMemberUseFeature(member, feature: "blindbox")
            
            print("AI分析功能: \(canUseAI ? "可用" : "不可用")")
            print("盲盒抽奖功能: \(canUseLottery ? "可用" : "不可用")")
        }
    }
    
    // MARK: - 统计分析示例
    
    /// 数据统计示例
    func statisticsExample() {
        // 获取全家积分统计
        let familyStats = dataManager.getFamilyPointStatistics()
        print("全家总积分: \(familyStats.total), 正分: \(familyStats.positive), 负分: \(familyStats.negative)")
        
        // 获取单个成员统计
        if let member = dataManager.members.first {
            let memberStats = dataManager.getPointStatistics(for: member)
            print("\(member.name ?? "未知成员") 积分统计: 总计\(memberStats.total), 正分\(memberStats.positive), 负分\(memberStats.negative)")

            // 获取本周统计
            let weekStart = Calendar.current.dateInterval(of: .weekOfYear, for: Date())
            if let weekInterval = weekStart {
                let weekStats = dataManager.getPointStatistics(for: member, in: weekInterval)
                print("\(member.name ?? "未知成员") 本周积分: \(weekStats.total)")
            }
        }
    }
}
