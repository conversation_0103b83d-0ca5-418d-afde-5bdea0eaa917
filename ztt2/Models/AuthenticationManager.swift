//
//  AuthenticationManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import Foundation
import AuthenticationServices
import SwiftUI
import CoreData

/**
 * 认证管理器
 * 负责管理用户登录状态和Apple登录流程
 * 确保用户数据正确关联Apple ID
 */
class AuthenticationManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = AuthenticationManager()
    
    // MARK: - Published Properties
    @Published var isLoggedIn: Bool = false
    @Published var currentUser: User?
    @Published var isLoading: Bool = false
    
    // MARK: - Private Properties
    private let persistenceController = PersistenceController.shared
    private let keychainManager = KeychainManager.shared
    
    // MARK: - Initialization
    private init() {
        checkLoginStatus()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查登录状态
     */
    func checkLoginStatus() {
        print("🔍 开始检查登录状态")
        isLoading = true
        
        // 从Keychain检查登录状态
        let savedLoginStatus = keychainManager.isLoggedIn()
        let savedAppleUserID = keychainManager.getAppleUserID()
        
        print("💾 Keychain中的登录状态: \(savedLoginStatus)")
        print("💾 Keychain中的Apple用户ID: \(savedAppleUserID ?? "无")")
        
        // 如果有Apple用户ID，验证Apple登录状态
        if let appleUserID = savedAppleUserID {
            print("✅ 检测到Apple用户ID，开始验证Apple登录状态")
            verifyAppleLoginStatus(userID: appleUserID)
        } else {
            print("❌ 未检测到Apple用户ID")
            // 未登录状态
            DispatchQueue.main.async {
                self.isLoggedIn = false
                self.isLoading = false
            }
        }
    }
    
    /**
     * 处理Apple登录成功
     */
    func handleSuccessfulLogin(userID: String, fullName: PersonNameComponents?, email: String?) {
        isLoading = true
        
        // 保存登录信息到Keychain
        let displayName = fullName != nil ? PersonNameComponentsFormatter().string(from: fullName!) : nil
        keychainManager.saveLoginInfo(
            appleUserID: userID,
            userName: displayName,
            userEmail: email
        )
        
        // 创建或更新用户记录
        createOrUpdateUser(appleUserID: userID, fullName: fullName, email: email) { [weak self] user in
            DispatchQueue.main.async {
                self?.currentUser = user
                self?.isLoggedIn = true
                self?.isLoading = false
                
                print("✅ 用户登录成功: \(user?.nickname ?? "Unknown")")
                
                // 通知DataManager刷新用户状态
                DataManager.shared.refreshCurrentUser()
            }
        }
    }
    
    /**
     * 处理登录失败
     */
    func handleLoginFailure(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            print("❌ 登录失败: \(error.localizedDescription)")
        }
    }
    
    /**
     * 退出登录
     */
    func logout() {
        // 清除Keychain中的登录信息
        keychainManager.clearLoginInfo()
        
        // 清除本地状态
        currentUser = nil
        isLoggedIn = false
        
        // 通知DataManager刷新用户状态
        Task { @MainActor in
            DataManager.shared.refreshCurrentUser()
        }
        
        print("🔄 用户已退出登录")
    }
    
    // MARK: - Private Methods
    
    /**
     * 验证Apple登录状态
     */
    private func verifyAppleLoginStatus(userID: String) {
        let provider = ASAuthorizationAppleIDProvider()
        provider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    // Apple登录状态有效，加载用户数据
                    self?.loadUserData(appleUserID: userID)
                case .revoked, .notFound:
                    // Apple登录状态无效，清除本地状态
                    self?.clearInvalidLoginState()
                case .transferred:
                    // 用户账号已转移，需要重新登录
                    self?.clearInvalidLoginState()
                @unknown default:
                    // 未知状态，清除本地状态
                    self?.clearInvalidLoginState()
                }
            }
        }
    }
    
    /**
     * 加载用户数据
     */
    private func loadUserData(appleUserID: String) {
        // 从CoreData中查找用户
        let user = getUserByAppleID(appleUserID)
        
        if let user = user {
            self.currentUser = user
            self.isLoggedIn = true
            print("✅ 用户数据加载成功: \(user.nickname ?? "Unknown") (Apple ID: \(appleUserID))")
            
            // 关联现有数据（如果有未关联的数据）
            linkUserWithExistingData(appleUserID: appleUserID, user: user)
            
            // 通知DataManager刷新用户状态
            Task { @MainActor in
                DataManager.shared.refreshCurrentUser()
            }
        } else {
            // 用户数据不存在，检查是否有未关联的用户数据可以迁移
            checkAndMigrateUnlinkedData(appleUserID: appleUserID) { [weak self] user in
                DispatchQueue.main.async {
                    self?.currentUser = user
                    self?.isLoggedIn = true
                    print("✅ 用户数据迁移完成: \(user?.nickname ?? "Unknown")")
                    
                    // 通知DataManager刷新用户状态
                    Task { @MainActor in
                        DataManager.shared.refreshCurrentUser()
                    }
                }
            }
        }
        
        self.isLoading = false
    }
    
    /**
     * 清除无效的登录状态
     */
    private func clearInvalidLoginState() {
        keychainManager.clearLoginInfo()
        
        self.currentUser = nil
        self.isLoggedIn = false
        self.isLoading = false
        
        print("🔄 已清除无效的登录状态")
    }
    
    /**
     * 创建或更新用户
     */
    private func createOrUpdateUser(
        appleUserID: String,
        fullName: PersonNameComponents?,
        email: String?,
        completion: @escaping (User?) -> Void
    ) {
        let context = persistenceController.container.viewContext
        
        // 查找现有用户
        let existingUser = getUserByAppleID(appleUserID)
        
        let user: User
        if let existingUser = existingUser {
            // 更新现有用户
            user = existingUser
        } else {
            // 创建新用户
            user = User(context: context)
            user.id = UUID()
            user.appleUserID = appleUserID
            user.createdAt = Date()
            user.nickname = "家长" // 默认昵称
        }
        
        // 更新用户信息
        if let fullName = fullName {
            let displayName = PersonNameComponentsFormatter().string(from: fullName)
            if !displayName.isEmpty {
                user.nickname = displayName
            }
        }
        
        if let email = email {
            user.email = email
        }
        
        // 确保用户有订阅信息
        if user.subscription == nil {
            let subscription = Subscription(context: context)
            subscription.id = UUID()
            subscription.subscriptionType = "free"
            subscription.isActive = true
            subscription.createdAt = Date()
            subscription.updatedAt = Date()
            subscription.user = user
        }
        
        // 保存到CoreData
        do {
            try context.save()
            completion(user)
            print("💾 用户信息保存成功")
        } catch {
            print("❌ 用户信息保存失败: \(error)")
            completion(nil)
        }
    }

    /**
     * 根据Apple用户ID查找用户
     */
    private func getUserByAppleID(_ appleUserID: String) -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == %@", appleUserID)
        request.fetchLimit = 1

        do {
            let users = try persistenceController.container.viewContext.fetch(request)
            return users.first
        } catch {
            print("❌ 查找用户失败: \(error)")
            return nil
        }
    }

    /**
     * 检查并迁移未关联的数据
     */
    private func checkAndMigrateUnlinkedData(appleUserID: String, completion: @escaping (User?) -> Void) {
        // 查找是否有未关联Apple ID的用户数据
        let context = persistenceController.container.viewContext
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == nil OR appleUserID == ''")

        do {
            let unlinkedUsers = try context.fetch(request)

            if let firstUnlinkedUser = unlinkedUsers.first {
                // 将第一个未关联用户与Apple ID关联
                firstUnlinkedUser.appleUserID = appleUserID

                // 更新用户信息
                let savedUserName = keychainManager.getUserName()
                let savedEmail = keychainManager.getUserEmail()

                if let savedUserName = savedUserName {
                    firstUnlinkedUser.nickname = savedUserName
                }
                if let savedEmail = savedEmail {
                    firstUnlinkedUser.email = savedEmail
                }

                // 删除其他未关联用户（避免重复数据）
                for i in 1..<unlinkedUsers.count {
                    context.delete(unlinkedUsers[i])
                }

                try context.save()
                print("🔄 已将现有数据关联到Apple ID: \(appleUserID)")
                print("📊 关联的成员数量: \(firstUnlinkedUser.members?.count ?? 0)")
                completion(firstUnlinkedUser)
            } else {
                // 没有未关联的数据，创建新用户
                createOrUpdateUser(appleUserID: appleUserID, fullName: nil, email: nil, completion: completion)
            }
        } catch {
            print("❌ 数据迁移失败: \(error)")
            completion(nil)
        }
    }

    /**
     * 根据Apple ID关联用户和现有数据
     */
    private func linkUserWithExistingData(appleUserID: String, user: User) {
        // 查找是否有其他用户的数据需要迁移
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "appleUserID == nil OR appleUserID == ''")

        do {
            let unlinkedUsers = try persistenceController.container.viewContext.fetch(request)

            for unlinkedUser in unlinkedUsers {
                if unlinkedUser != user {
                    // 迁移成员数据
                    if let members = unlinkedUser.members?.allObjects as? [Member] {
                        for member in members {
                            member.user = user
                            print("🔄 迁移成员: \(member.name ?? "Unknown") 到用户: \(user.nickname ?? "Unknown")")
                        }
                    }

                    // 迁移订阅信息
                    if let subscription = unlinkedUser.subscription {
                        if user.subscription == nil {
                            subscription.user = user
                            user.subscription = subscription
                            print("🔄 迁移订阅信息到用户: \(user.nickname ?? "Unknown")")
                        }
                    }

                    // 删除旧用户
                    persistenceController.container.viewContext.delete(unlinkedUser)
                    print("🗑️ 删除未关联用户")
                }
            }

            persistenceController.save()
            print("✅ 用户数据关联完成")

        } catch {
            print("❌ 数据关联失败: \(error)")
        }
    }
}
