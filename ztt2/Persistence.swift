//
//  Persistence.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import CoreData
import CloudKit

/**
 * 持久化控制器
 * 统一使用CloudKit同步，支持所有用户多设备同步
 */
struct PersistenceController {
    static let shared = PersistenceController()

    // CloudKit支持标志 - 现在所有用户都启用CloudKit
    private(set) var isCloudKitEnabled: Bool = true

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "示例用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        // 创建示例订阅
        let subscription = Subscription(context: viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        // 创建示例家庭成员
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    // MARK: - Initialization

    init(inMemory: Bool = false) {
        // 统一使用CloudKit容器，支持所有用户多设备同步
        container = NSPersistentCloudKitContainer(name: "ztt2")

        print("☁️ 配置CloudKit同步容器 - 统一多设备同步模式")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            Self.configureStoreDescription(container.persistentStoreDescriptions.first!)
        }

        setupContainer()
    }

    // MARK: - Store Configuration

    /**
     * 配置CloudKit存储描述
     */
    private static func configureStoreDescription(_ description: NSPersistentStoreDescription) {
        // 启用CloudKit同步相关配置
        description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

        // CloudKit 容器配置
        description.setOption("iCloud.com.rainkygong.ztt2" as NSString,
                            forKey: "containerIdentifier")

        // 使用默认存储位置
        description.url = defaultStoreURL()

        // 确保自动迁移
        description.setOption(true as NSNumber, forKey: NSMigratePersistentStoresAutomaticallyOption)
        description.setOption(true as NSNumber, forKey: NSInferMappingModelAutomaticallyOption)

        // 确保数据库目录存在
        if let storeURL = description.url {
            let storeDirectory = storeURL.deletingLastPathComponent()
            do {
                try FileManager.default.createDirectory(at: storeDirectory, withIntermediateDirectories: true, attributes: nil)
                print("Core Data store directory created: \(storeDirectory.path)")
            } catch {
                print("Failed to create store directory: \(error)")
            }
        }

        print("Core Data store URL: \(description.url?.path ?? "Unknown")")
    }

    /**
     * 默认存储URL（CloudKit同步）
     */
    private static func defaultStoreURL() -> URL {
        return NSPersistentContainer.defaultDirectoryURL()
            .appendingPathComponent("ztt2.sqlite")
    }

    /**
     * 设置容器
     */
    private func setupContainer() {
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                /*
                 典型的错误原因包括：
                 * 父目录不存在、无法创建或不允许写入
                 * 由于权限或设备锁定时的数据保护，无法访问持久存储
                 * 设备空间不足
                 * 存储无法迁移到当前模型版本
                 * CloudKit 配置问题
                 检查错误消息以确定实际问题
                 */
                print("❌ Core Data error: \(error), \(error.userInfo)")

                // 如果是权限错误，记录错误信息
                if error.code == 513 { // NSCocoaErrorDomain Code=513
                    print("检测到权限错误，可能需要重新安装应用")
                    return
                }

                fatalError("Unresolved error \(error), \(error.userInfo)")
            } else {
                print("✅ Core Data store loaded successfully: \(storeDescription.url?.path ?? "Unknown")")
                print("☁️ CloudKit同步已启用，数据将自动同步到所有设备")
            }
        })

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true

        // 设置CloudKit远程变更通知
        setupCloudKitNotifications()
    }

    /**
     * 设置CloudKit远程变更通知
     */
    private func setupCloudKitNotifications() {
        // 监听CloudKit远程变更
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { _ in
            print("📥 CloudKit远程变更检测到，刷新本地数据")
            self.container.viewContext.refreshAllObjects()

            // 发送CloudKit同步完成通知，通知UI更新统计数据
            NotificationCenter.default.post(
                name: NSNotification.Name("CloudKitSyncCompleted"),
                object: nil
            )
            print("📊 已发送CloudKit同步完成通知")
        }
    }


}

// MARK: - 数据操作扩展
extension PersistenceController {

    /// 保存上下文
    func save() {
        let context = container.viewContext

        if context.hasChanges {
            do {
                try context.save()
                print("✅ Core Data保存成功，CloudKit同步将自动触发")
            } catch {
                let nsError = error as NSError
                print("❌ Core Data保存失败: \(nsError.localizedDescription)")

                // 检查是否是文件不存在的错误，尝试重新初始化
                if nsError.code == 134030 || nsError.code == 4 {
                    container.loadPersistentStores { (storeDescription, error) in
                        if error == nil {
                            // 重新尝试保存
                            DispatchQueue.main.async {
                                do {
                                    try context.save()
                                    print("✅ Core Data重新保存成功")
                                } catch {
                                    print("❌ 重新保存失败: \(error.localizedDescription)")
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// 触发CloudKit同步
    func triggerCloudKitSync() {
        print("🔄 手动触发CloudKit同步...")

        // 强制保存当前更改
        save()

        // 刷新所有对象以获取最新的CloudKit数据
        container.viewContext.refreshAllObjects()

        print("✅ CloudKit同步已触发")
    }

    /// 获取当前用户
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try container.viewContext.fetch(request)
            return users.first
        } catch {
            print("Failed to fetch user: \(error)")
            return nil
        }
    }

    // 注意：已移除createDefaultUserIfNeeded方法
    // 现在用户必须通过Apple ID登录才能创建账号，确保数据正确关联

    // MARK: - CloudKit Sync Status

    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return isCloudKitEnabled
    }


}
