//
//  LotteryPermissionTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 抽奖权限测试视图
 * 用于测试抽奖功能的权限检查和引导订阅流程
 */
struct LotteryPermissionTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showLotteryOptions = false
    @State private var currentUserLevel = "免费用户"
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("抽奖权限测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)

                // 用户状态诊断
                VStack(spacing: 10) {
                    Text("用户状态诊断")
                        .font(.headline)

                    VStack(spacing: 5) {
                        Text("DataManager用户: \(dataManager.currentUser?.nickname ?? "空")")
                            .font(.caption)
                            .foregroundColor(dataManager.currentUser != nil ? .green : .red)

                        Text("测试用户: \(testMember?.user?.nickname ?? "空")")
                            .font(.caption)
                            .foregroundColor(testMember?.user != nil ? .green : .red)

                        Text("当前等级: \(currentUserLevel)")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 会员等级切换按钮
                VStack(spacing: 15) {
                    Text("切换会员等级测试")
                        .font(.headline)
                    
                    HStack(spacing: 15) {
                        Button("免费用户") {
                            setUserLevel("free")
                        }
                        .buttonStyle(TestButtonStyle(isSelected: currentUserLevel == "免费用户"))
                        
                        Button("初级会员") {
                            setUserLevel("basic")
                        }
                        .buttonStyle(TestButtonStyle(isSelected: currentUserLevel == "初级会员"))
                        
                        Button("高级会员") {
                            setUserLevel("premium")
                        }
                        .buttonStyle(TestButtonStyle(isSelected: currentUserLevel == "高级会员"))
                    }
                }
                
                // 权限检查结果
                VStack(spacing: 10) {
                    Text("权限检查结果")
                        .font(.headline)
                    
                    VStack(spacing: 8) {
                        PermissionCheckRow(
                            feature: "大转盘",
                            hasPermission: checkPermission(for: "wheel")
                        )
                        
                        PermissionCheckRow(
                            feature: "盲盒",
                            hasPermission: checkPermission(for: "blindbox")
                        )
                        
                        PermissionCheckRow(
                            feature: "刮刮卡",
                            hasPermission: checkPermission(for: "scratchcard")
                        )
                        
                        PermissionCheckRow(
                            feature: "AI分析",
                            hasPermission: checkPermission(for: "ai_analysis")
                        )
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 用户管理按钮
                HStack(spacing: 15) {
                    Button("刷新用户状态") {
                        dataManager.refreshCurrentUser()
                        updateCurrentUserLevel()
                    }
                    .buttonStyle(TestButtonStyle(isSelected: false))

                    Button("重新设置测试数据") {
                        setupTestData()
                    }
                    .buttonStyle(TestButtonStyle(isSelected: false))
                }

                // 测试抽奖选项弹窗
                Button("测试抽奖选项弹窗") {
                    showLotteryOptions = true
                }
                .buttonStyle(PrimaryButtonStyle())

                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationTitle("权限测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            setupTestData()
        }
        .overlay(
            // 抽奖选项弹窗
            showLotteryOptions ? 
            LotteryOptionsView(
                isPresented: $showLotteryOptions,
                onWheelSelected: {
                    print("✅ 大转盘选择成功")
                },
                onBlindBoxSelected: {
                    print("✅ 盲盒选择成功")
                },
                onScratchCardSelected: {
                    print("✅ 刮刮卡选择成功")
                },
                onNavigateToSubscription: {
                    print("🔄 导航到订阅页面")
                },
                onWheelConfigSelected: {
                    print("⚙️ 配置大转盘")
                },
                onBlindBoxConfigSelected: {
                    print("⚙️ 配置盲盒")
                },
                onScratchCardConfigSelected: {
                    print("⚙️ 配置刮刮卡")
                }
            ) : nil
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置测试数据
     */
    private func setupTestData() {
        // 创建测试成员（如果不存在）
        if dataManager.members.isEmpty {
            testMember = dataManager.createMember(
                name: "测试成员",
                role: "son",
                birthDate: Date(),
                initialPoints: 100
            )
        } else {
            testMember = dataManager.members.first
        }
        
        updateCurrentUserLevel()
    }
    
    /**
     * 设置用户等级
     */
    private func setUserLevel(_ level: String) {
        guard dataManager.currentUser != nil else { return }
        
        // 更新订阅状态
        dataManager.updateSubscription(
            type: level,
            isActive: level != "free",
            startDate: Date(),
            endDate: level != "free" ? Calendar.current.date(byAdding: .year, value: 1, to: Date()) : nil,
            productIdentifier: level == "premium" ? "com.ztt.premium.yearly" : (level == "basic" ? "com.ztt.basic.yearly" : nil)
        )
        
        updateCurrentUserLevel()
    }
    
    /**
     * 更新当前用户等级显示
     */
    private func updateCurrentUserLevel() {
        guard let user = dataManager.currentUser else {
            currentUserLevel = "未知用户"
            return
        }
        
        if user.isPremiumMember {
            currentUserLevel = "高级会员"
        } else if user.isBasicMemberOrAbove {
            currentUserLevel = "初级会员"
        } else {
            currentUserLevel = "免费用户"
        }
    }
    
    /**
     * 检查权限
     */
    private func checkPermission(for feature: String) -> Bool {
        guard let member = testMember else { return false }
        return dataManager.canMemberUseFeature(member, feature: feature)
    }
}

/**
 * 权限检查行组件
 */
struct PermissionCheckRow: View {
    let feature: String
    let hasPermission: Bool
    
    var body: some View {
        HStack {
            Text(feature)
                .font(.body)
            
            Spacer()
            
            Image(systemName: hasPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(hasPermission ? .green : .red)
                .font(.title3)
        }
    }
}

/**
 * 测试按钮样式
 */
struct TestButtonStyle: ButtonStyle {
    let isSelected: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(isSelected ? .white : .blue)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue : Color.blue.opacity(0.1))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

/**
 * 主要按钮样式
 */
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.white)
            .padding(.horizontal, 30)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview
#Preview {
    LotteryPermissionTestView()
        .environmentObject(DataManager.shared)
}
