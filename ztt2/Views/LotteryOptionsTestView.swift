//
//  LotteryOptionsTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖选项弹窗测试视图
 * 用于测试和预览抽奖选项弹窗的功能
 */
struct LotteryOptionsTestView: View {
    
    @State private var showLotteryOptions = false
    @State private var selectedOption = ""
    @State private var showResult = false
    
    var body: some View {
        ZStack {
            // 背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f0f8e8"),
                    Color(hex: "#e8f5d6")
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // 标题
                Text("抽奖弹窗测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 测试按钮
                Button(action: {
                    showLotteryOptions = true
                }) {
                    HStack {
                        Image(systemName: "gift.fill")
                            .font(.system(size: 20))
                        Text("打开抽奖选项")
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 15)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#a9d051"),
                                Color(hex: "#87c441")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                }
                
                // 结果显示
                if showResult {
                    VStack(spacing: 10) {
                        Text("选择结果:")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text(selectedOption)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(Color.white)
                            .cornerRadius(10)
                            .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
                
                Spacer()
                
                // 说明文字
                VStack(spacing: 8) {
                    Text("测试说明:")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Text("点击按钮测试抽奖选项弹窗\n包含大转盘、盲盒、刮刮卡三个选项")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 50)
            }
            
            // 抽奖选项弹窗
            if showLotteryOptions {
                LotteryOptionsView(
                    isPresented: $showLotteryOptions,
                    onWheelSelected: {
                        selectedOption = "大转盘"
                        showResultWithAnimation()
                    },
                    onBlindBoxSelected: {
                        selectedOption = "盲盒"
                        showResultWithAnimation()
                    },
                    onScratchCardSelected: {
                        selectedOption = "刮刮卡"
                        showResultWithAnimation()
                    },
                    onNavigateToSubscription: {
                        selectedOption = "导航到订阅页面"
                        showResultWithAnimation()
                    },
                    onWheelConfigSelected: {
                        selectedOption = "配置大转盘"
                        showResultWithAnimation()
                    },
                    onBlindBoxConfigSelected: {
                        selectedOption = "配置盲盒"
                        showResultWithAnimation()
                    },
                    onScratchCardConfigSelected: {
                        selectedOption = "配置刮刮卡"
                        showResultWithAnimation()
                    }
                )
            }
        }
        .navigationTitle("抽奖弹窗测试")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func showResultWithAnimation() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            showResult = true
        }
        
        // 3秒后隐藏结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            withAnimation(.easeOut(duration: 0.3)) {
                showResult = false
            }
        }
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        LotteryOptionsTestView()
    }
}
