//
//  MemberDeleteConfirmationDialog.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/4.
//

import SwiftUI

/**
 * 成员删除确认对话框组件
 * 与项目UI风格保持一致的自定义模态对话框
 * 兼容iOS15.6以上版本
 */
struct MemberDeleteConfirmationDialog: View {
    
    @Binding var isPresented: Bool
    let memberName: String
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        onCancel()
                    }
                    .transition(.opacity)
                
                // 确认对话框主体
                VStack(spacing: 0) {
                    // 标题区域
                    VStack(spacing: 10) {
                        // 警告图标
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 32))
                            .foregroundColor(.orange)
                            .scaleEffect(animationTrigger ? 1.1 : 1.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.7).repeatCount(2, autoreverses: true), value: animationTrigger)

                        // 标题文本
                        Text("删除成员")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        // 确认信息 - 简化为一行
                        HStack(spacing: 4) {
                            Text("确定要删除成员")
                                .font(.system(size: 15))
                                .foregroundColor(DesignSystem.Colors.textSecondary)

                            Text("「\(memberName)」")
                                .font(.system(size: 15, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)

                            Text("吗？")
                                .font(.system(size: 15))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }

                        // 警告提示 - 简化文字
                        Text("删除后无法恢复")
                            .font(.system(size: 13))
                            .foregroundColor(.red)
                            .padding(.top, 4)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    
                    // 分割线
                    Divider()
                        .background(DesignSystem.Colors.textTertiary)
                    
                    // 按钮组
                    HStack(spacing: 0) {
                        // 取消按钮
                        Button(action: onCancel) {
                            Text("取消")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                        }
                        .buttonStyle(PlainButtonStyle())

                        // 垂直分割线
                        Divider()
                            .background(DesignSystem.Colors.textTertiary)

                        // 确定删除按钮
                        Button(action: onConfirm) {
                            Text("删除")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.red)
                                .frame(maxWidth: .infinity)
                                .frame(height: 44)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .frame(maxWidth: 280)
                .fixedSize(horizontal: false, vertical: true)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(DesignSystem.Colors.studentCardBorder, lineWidth: 1)
                )
                .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                .scaleEffect(isPresented ? 1.0 : 0.9)
                .opacity(isPresented ? 1.0 : 0.0)
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isPresented)
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                // 延迟触发警告图标动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                // 重置动画状态
                animationTrigger = false
                // 延迟触发警告图标动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    animationTrigger = true
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        MemberDeleteConfirmationDialog(
            isPresented: .constant(true),
            memberName: "小明",
            onConfirm: {
                print("确认删除成员")
            },
            onCancel: {
                print("取消删除")
            }
        )
    }
}
