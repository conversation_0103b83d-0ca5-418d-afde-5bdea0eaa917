//
//  LiquidTabBarBubble.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 液态导航栏融球组件
 * 负责显示黄色圆球背景和选中的图标
 */
struct LiquidTabBarBubble: View {

    // MARK: - Properties

    /// 融球中心X坐标
    let centerX: CGFloat

    /// 当前选中的图标名称
    let selectedIcon: String

    /// 语音识别服务（可选，仅在成长日记页面使用）
    var speechRecognitionService: SpeechRecognitionService?

    // MARK: - State
    @State private var isPressed = false
    
    // MARK: - Constants

    private var bubbleSize: CGFloat {
        // 录音时稍微放大
        if selectedIcon == "录音" && speechRecognitionService?.isRecording == true {
            return DesignSystem.LiquidTabBar.bubbleSize * 1.1
        }
        return DesignSystem.LiquidTabBar.bubbleSize
    }

    private var iconSize: CGFloat {
        // 根据不同图标调整大小
        switch selectedIcon {
        case "录音":
            return DesignSystem.LiquidTabBar.bubbleIconSize * 1.3
        default:
            return DesignSystem.LiquidTabBar.bubbleIconSize
        }
    }

    private var bubbleColor: Color {
        // 录音时使用红色，否则使用默认颜色
        if selectedIcon == "录音" && speechRecognitionService?.isRecording == true {
            return Color.red.opacity(0.8)
        }
        return DesignSystem.LiquidTabBar.bubbleColor
    }
    
    // MARK: - View
    
    var body: some View {
        GeometryReader { geometry in
            // 融球背景
            Circle()
                .fill(bubbleColor)
                .frame(width: bubbleSize, height: bubbleSize)
                .overlay(
                    // 图标
                    Image(selectedIcon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: iconSize, height: iconSize)
                        .foregroundColor(DesignSystem.LiquidTabBar.bubbleIconColor)
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
                .animation(.easeInOut(duration: 0.3), value: speechRecognitionService?.isRecording)
                .position(
                    x: centerX,
                    y: geometry.size.height / 2 - 10 // 向上偏移10px以配合上拱效果
                )
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    // 只有在录音图标且有语音识别服务时才响应长按
                    guard selectedIcon == "录音", let speechService = speechRecognitionService else { return }

                    isPressed = pressing

                    if pressing {
                        // 开始录音
                        Task {
                            do {
                                try await speechService.startRecording()
                            } catch {
                                print("开始录音失败: \(error)")
                            }
                        }
                    } else {
                        // 停止录音
                        speechService.stopRecording()
                    }
                }, perform: {})
        }
    }
}

// MARK: - Preview
#Preview {
    VStack {
        Spacer()
        
        ZStack {
            Rectangle()
                .fill(Color.white)
                .frame(height: 72)
            
            LiquidTabBarBubble(
                centerX: 150,
                selectedIcon: "shouye1_1",
                speechRecognitionService: nil
            )
            .frame(height: 72)
        }
    }
    .background(Color.gray.opacity(0.2))
}
