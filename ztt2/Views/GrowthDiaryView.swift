//
//  GrowthDiaryView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 成长日记页面
 * 根据需求文档设计，包含大输入框、时间选择器、保存和查看历史按钮
 */
struct GrowthDiaryView: View {

    // MARK: - State Properties
    @State private var diaryTitle: String = ""
    @State private var diaryContent: String = ""
    @State private var selectedDate: Date = Date()
    @State private var showDatePicker: Bool = false
    @State private var isLoading: Bool = false
    @State private var showSuccessAlert: Bool = false
    @State private var showErrorAlert: Bool = false
    @State private var errorMessage: String = ""
    @State private var pageAppeared: Bool = false

    // 选择对象相关状态
    @State private var selectedMember: Member? = nil
    @State private var showMemberPicker: Bool = false

    // 历史日记查看相关状态
    @State private var showHistoryMemberPicker: Bool = false
    @State private var showHistorySheet: Bool = false
    @State private var selectedHistoryMember: Member? = nil
    @State private var historyDiaryEntries: [DiaryEntry] = []
    @State private var showEditDiarySheet: Bool = false
    @State private var editingDiary: DiaryEntry? = nil

    // 视图模型
    @StateObject private var viewModel = GrowthDiaryViewModel()

    // 语音识别服务
    @EnvironmentObject var speechRecognitionService: SpeechRecognitionService

    // 获取儿子和女儿角色的成员
    private var children: [Member] {
        return viewModel.getChildren()
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变 - 与首页保持一致的风格
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)

                // 装饰性背景元素
                VStack {
                    HStack {
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 100, height: 100)
                            .offset(x: -30, y: 20)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.04))
                            .frame(width: 120, height: 120)
                            .offset(x: 40, y: -10)
                    }
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.02))
                            .frame(width: 80, height: 80)
                            .offset(x: 20, y: 30)
                    }
                }

                // 主要内容区域
                VStack(spacing: 0) {
                    // 标题区域
                    titleSection

                    // 内容区域
                    contentSection(geometry: geometry)
                }
                .padding(.top, DesignSystem.Spacing.lg)
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.bottom, DesignSystem.Spacing.md)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6)) {
                pageAppeared = true
            }

            // 请求语音识别权限
            Task {
                await speechRecognitionService.requestPermissions()
            }

            // 恢复缓存的内容
            loadDraftContent()
        }
        .alert("growth_diary.save.success".localized, isPresented: $showSuccessAlert) {
            Button("common.button.confirm".localized) { }
        }
        .alert("growth_diary.save.error".localized, isPresented: $showErrorAlert) {
            Button("common.button.confirm".localized) { }
        } message: {
            Text(errorMessage)
        }
        .overlay(
            // 日期选择器弹窗
            DatePickerPopupView(
                isPresented: $showDatePicker,
                selectedDate: $selectedDate,
                onConfirm: {
                    // 日期选择确认后的处理
                }
            )
        )
        .overlay(
            // 成员选择器弹窗
            MemberPickerPopupView(
                isPresented: $showMemberPicker,
                selectedMember: $selectedMember,
                children: children,
                onConfirm: {
                    // 成员选择确认后的处理
                    showMemberPicker = false
                }
            )
        )
        .sheet(isPresented: $showHistoryMemberPicker) {
            historyMemberPickerSheet
        }
        .sheet(isPresented: $showHistorySheet) {
            historyReportSheet
        }
        .sheet(isPresented: $showEditDiarySheet) {
            editDiarySheet
        }
        .keyboardToolbar()
        .onChange(of: speechRecognitionService.recognizedText) { recognizedText in
            // 当语音识别文本变化时，更新日记内容
            if !recognizedText.isEmpty {
                diaryContent = recognizedText
                // 缓存内容到UserDefaults
                saveDraftContent()
            }
        }
        .onChange(of: diaryContent) { content in
            // 当用户手动编辑内容时也进行缓存
            saveDraftContent()
        }
        .onChange(of: diaryTitle) { title in
            // 当用户编辑标题时也进行缓存
            saveDraftContent()
        }
        .alert("语音识别错误", isPresented: .constant(speechRecognitionService.errorMessage != nil)) {
            Button("确定") {
                speechRecognitionService.clearError()
            }
        } message: {
            if let errorMessage = speechRecognitionService.errorMessage {
                Text(errorMessage)
            }
        }
    }

    // MARK: - Title Section
    private var titleSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            HStack {
                Text("growth_diary.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingLarge.fontSize,
                        weight: DesignSystem.Typography.HeadingLarge.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .opacity(pageAppeared ? 1.0 : 0.0)
                    .offset(y: pageAppeared ? 0 : -20)
                    .animation(.easeOut(duration: 0.8).delay(0.2), value: pageAppeared)

                Spacer()

                // 标题输入框
                titleInputField
            }
        }
        .padding(.bottom, DesignSystem.Spacing.lg)
    }

    // MARK: - Title Input Field
    private var titleInputField: some View {
        TextField("growth_diary.title.input.placeholder".localized, text: $diaryTitle)
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.sm)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(Color.white.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 1)
                    )
            )
            .frame(width: 200)
            .opacity(pageAppeared ? 1.0 : 0.0)
            .offset(y: pageAppeared ? 0 : -20)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: pageAppeared)
    }

    // MARK: - Content Section
    private func contentSection(geometry: GeometryProxy) -> some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 大输入框 - 占屏幕高度的50%
            diaryInputSection(geometry: geometry)

            // 时间选择器和对象选择器
            dateAndMemberSelectionSection

            // 按钮区域
            buttonSection

            Spacer()
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.4), value: pageAppeared)
    }

    // MARK: - Diary Input Section
    private func diaryInputSection(geometry: GeometryProxy) -> some View {
        let inputHeight = geometry.size.height * 0.5

        return VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            ZStack(alignment: .topLeading) {
                // 背景
                RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                    .foregroundColor(DesignSystem.Colors.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                            .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 2)
                    )
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)

                // 输入框
                TextEditor(text: $diaryContent)
                    .font(.system(size: DesignSystem.Typography.Body.fontSize))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(DesignSystem.Spacing.md)
                    .background(Color.clear)

                // 占位符
                if diaryContent.isEmpty {
                    Text("growth_diary.input.placeholder".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.top, DesignSystem.Spacing.md + 8)
                        .padding(.leading, DesignSystem.Spacing.md + 4)
                        .allowsHitTesting(false)
                }
            }
            .frame(height: inputHeight)
        }
    }

    // MARK: - Date and Member Selection Section
    private var dateAndMemberSelectionSection: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            // 记录时间按钮 - 减少宽度
            Button(action: {
                showDatePicker = true
            }) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "calendar")
                        .foregroundColor(Color(hex: "#B5E36B"))
                        .font(.system(size: 16, weight: .medium))

                    VStack(alignment: .leading, spacing: 2) {
                        Text(formatDate(selectedDate))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "#B5E36B"))
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.system(size: 12, weight: .medium))
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .frame(maxWidth: .infinity) // 占用可用空间的一半

            // 选择对象按钮
            Button(action: {
                showMemberPicker = true
            }) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "person.circle")
                        .foregroundColor(Color(hex: "#B5E36B"))
                        .font(.system(size: 16, weight: .medium))

                    VStack(alignment: .leading, spacing: 2) {
                        Text(selectedMember?.displayName ?? "growth_diary.member.select".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "#B5E36B"))
                            .lineLimit(1)
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.system(size: 12, weight: .medium))
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .frame(maxWidth: .infinity) // 占用可用空间的一半
        }
    }

    // MARK: - Button Section
    private var buttonSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 保存日记按钮
            Button(action: saveDiary) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "square.and.arrow.down")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text("growth_diary.button.save".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color(hex: "#B5E36B"))
                .cornerRadius(DesignSystem.Radius.md)
                .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(isLoading || diaryContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            .opacity(diaryContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)

            // 查看历史日记按钮
            Button(action: viewHistoryDiary) {
                HStack {
                    Image(systemName: "book")
                        .font(.system(size: 16, weight: .medium))

                    Text("growth_diary.button.view_history".localized)
                        .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                }
                .foregroundColor(Color(hex: "#B5E36B"))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(DesignSystem.Colors.cardBackground)
                .cornerRadius(DesignSystem.Radius.md)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                        .stroke(Color(hex: "#B5E36B"), lineWidth: 2)
                )
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
        }
    }

    // MARK: - History Member Picker Sheet
    private var historyMemberPickerSheet: some View {
        NavigationView {
            historyMemberPickerContent
                .navigationTitle("growth_diary.history.title".localized)
                .navigationBarTitleDisplayMode(.inline)
                .navigationBarBackButtonHidden(true)
                .navigationBarItems(
                    leading: Button("common.button.cancel".localized) {
                        showHistoryMemberPicker = false
                    }
                )
        }
    }

    private var historyMemberPickerContent: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            if children.isEmpty {
                historyEmptyStateView
            } else {
                historyMemberListView
            }
            Spacer()
        }
    }

    private var historyEmptyStateView: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Text("growth_diary.history.empty".localized)
                .font(.system(size: DesignSystem.Typography.Body.fontSize))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }

    private var historyMemberListView: some View {
        LazyVStack(spacing: DesignSystem.Spacing.sm) {
            ForEach(children, id: \.id) { child in
                historyMemberRow(child: child)
            }
        }
        .padding(.horizontal)
    }

    private func historyMemberRow(child: Member) -> some View {
        Button(action: {
            selectedHistoryMember = child
            loadHistoryDiaryEntries(for: child)
            showHistoryMemberPicker = false
            showHistorySheet = true
        }) {
            historyMemberRowContent(child: child)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func historyMemberRowContent(child: Member) -> some View {
        HStack {
            // 头像
            Image(child.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 40, height: 40)
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 2) {
                Text(child.displayName)
                    .font(.system(size: DesignSystem.Typography.Body.fontSize, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(child.roleDisplayName)
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .font(.system(size: 14))
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.cardBackground)
        .cornerRadius(DesignSystem.Radius.md)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - History Report Sheet
    private var historyReportSheet: some View {
        NavigationView {
            // 直接显示历史成长日记内容
            historyDiaryListView
                .navigationTitle("历史成长日记")
                .navigationBarTitleDisplayMode(.inline)
                .navigationBarBackButtonHidden(true)
                .navigationBarItems(
                    leading: Button("common.button.close".localized) {
                        showHistorySheet = false
                    }
                )
        }
    }

    // MARK: - History Diary List View
    private var historyDiaryListView: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                if historyDiaryEntries.isEmpty {
                    // 空状态
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "book")
                            .font(.system(size: 48))
                            .foregroundColor(Color(hex: "#B5E36B"))

                        Text("暂无成长日记")
                            .font(.system(size: DesignSystem.Typography.HeadingMedium.fontSize, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        Text("还没有为该成员记录成长日记\n快去记录第一篇日记吧")
                            .font(.system(size: DesignSystem.Typography.Body.fontSize))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .background(DesignSystem.Colors.cardBackground)
                    .cornerRadius(DesignSystem.Radius.lg)
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
                } else {
                    // 成长统计信息
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Text("成长统计")
                            .font(.system(size: DesignSystem.Typography.HeadingMedium.fontSize, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)

                        HStack(spacing: DesignSystem.Spacing.lg) {
                            VStack {
                                Text("\(historyDiaryEntries.count)")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(Color(hex: "#B5E36B"))
                                Text("日记总数")
                                    .font(.system(size: 12))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }

                            VStack {
                                Text("\(getDaysWithDiary())")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(Color(hex: "#FFE49E"))
                                Text("记录天数")
                                    .font(.system(size: 12))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                    }
                    .padding()
                    .background(DesignSystem.Colors.cardBackground)
                    .cornerRadius(DesignSystem.Radius.lg)
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)

                    // 日记列表
                    LazyVStack(spacing: DesignSystem.Spacing.sm) {
                        ForEach(historyDiaryEntries, id: \.id) { entry in
                            diaryEntryRow(entry: entry)
                        }
                    }
                }

                Spacer()
            }
            .padding()
        }
    }



    // MARK: - Diary Entry Row
    private func diaryEntryRow(entry: DiaryEntry) -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(formatDate(entry.timestamp ?? Date()))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "#B5E36B"))

                    // 显示标题（如果有的话）
                    if let title = entry.title, !title.isEmpty {
                        Text(title)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .lineLimit(1)

                        Text(entry.content ?? "")
                            .font(.system(size: DesignSystem.Typography.Body.fontSize))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    } else {
                        // 如果没有标题，显示内容的前几个字符作为标题
                        let displayTitle = getDisplayTitle(from: entry.content ?? "")
                        Text(displayTitle)
                            .font(.system(size: DesignSystem.Typography.Body.fontSize))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .lineLimit(3)
                            .multilineTextAlignment(.leading)
                    }
                }

                Spacer()

                Menu {
                    Button(action: {
                        editingDiary = entry
                        // 先关闭历史弹窗，然后打开编辑弹窗
                        showHistorySheet = false
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            showEditDiarySheet = true
                        }
                    }) {
                        Label("编辑", systemImage: "pencil")
                    }

                    Button(role: .destructive, action: {
                        deleteDiaryEntry(entry)
                    }) {
                        Label("删除", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.system(size: 16))
                        .padding(8)
                }
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.cardBackground)
        .cornerRadius(DesignSystem.Radius.md)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - Edit Diary Sheet
    private var editDiarySheet: some View {
        NavigationView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                // 日期显示（不可编辑）
                HStack {
                    Text("记录时间:")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Spacer()

                    Text(formatDate(editingDiary?.timestamp ?? Date()))
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.horizontal)

                // 标题编辑
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    Text("日记标题:")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    TextField("growth_diary.title.input.placeholder".localized, text: Binding(
                        get: { editingDiary?.title ?? "" },
                        set: { newValue in
                            editingDiary?.title = newValue
                        }
                    ))
                    .font(.system(size: DesignSystem.Typography.Body.fontSize))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(DesignSystem.Spacing.sm)
                    .background(DesignSystem.Colors.cardBackground)
                    .cornerRadius(DesignSystem.Radius.md)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                            .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 1)
                    )
                }
                .padding(.horizontal)

                // 内容编辑
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    Text("日记内容:")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    TextEditor(text: Binding(
                        get: { editingDiary?.content ?? "" },
                        set: { newValue in
                            editingDiary?.content = newValue
                        }
                    ))
                    .font(.system(size: DesignSystem.Typography.Body.fontSize))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(DesignSystem.Spacing.sm)
                    .background(DesignSystem.Colors.cardBackground)
                    .cornerRadius(DesignSystem.Radius.md)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Radius.md)
                            .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 1)
                    )
                    .frame(minHeight: 200)
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("编辑日记")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    showEditDiarySheet = false
                    editingDiary = nil
                    // 重新打开历史弹窗
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        showHistorySheet = true
                    }
                },
                trailing: Button("保存") {
                    saveEditedDiary()
                }
                .disabled(editingDiary?.content?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true)
            )
        }
    }

    // MARK: - Helper Methods

    /**
     * 格式化日期显示
     */
    private func formatDate(_ date: Date) -> String {
        return viewModel.formatDate(date, style: .medium)
    }

    /**
     * 从内容中生成显示标题
     */
    private func getDisplayTitle(from content: String) -> String {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        if trimmedContent.isEmpty {
            return "无内容"
        }

        // 取前20个字符作为标题
        let maxLength = 20
        if trimmedContent.count <= maxLength {
            return trimmedContent
        } else {
            let index = trimmedContent.index(trimmedContent.startIndex, offsetBy: maxLength)
            return String(trimmedContent[..<index]) + "..."
        }
    }

    /**
     * 获取有日记记录的天数
     */
    private func getDaysWithDiary() -> Int {
        guard let member = selectedHistoryMember else { return 0 }
        let statistics = viewModel.getDiaryStatistics(for: member)
        return statistics.uniqueDays
    }

    /**
     * 加载历史日记条目
     */
    private func loadHistoryDiaryEntries(for member: Member) {
        historyDiaryEntries = viewModel.getDiaryEntries(for: member)
    }

    /**
     * 保存日记
     */
    private func saveDiary() {
        guard let member = selectedMember else {
            errorMessage = "请选择记录对象"
            showErrorAlert = true
            return
        }

        isLoading = true

        viewModel.saveDiary(title: diaryTitle.isEmpty ? nil : diaryTitle, content: diaryContent, timestamp: selectedDate, for: member) { success, error in
            DispatchQueue.main.async {
                isLoading = false

                if success {
                    // 显示成功提示
                    showSuccessAlert = true

                    // 清空输入框和重置选择
                    diaryTitle = ""
                    diaryContent = ""
                    selectedMember = nil
                    selectedDate = Date()

                    // 清空语音识别累积文本
                    speechRecognitionService.clearAccumulatedText()

                    // 清空草稿缓存
                    clearDraftContent()
                } else {
                    errorMessage = error ?? "保存失败"
                    showErrorAlert = true
                }
            }
        }
    }

    /**
     * 保存编辑的日记
     */
    private func saveEditedDiary() {
        guard let diary = editingDiary else { return }

        viewModel.updateDiary(diary, title: diary.title, content: diary.content, timestamp: diary.timestamp) { success, error in
            DispatchQueue.main.async {
                if success {
                    // 刷新历史列表
                    if let member = selectedHistoryMember {
                        loadHistoryDiaryEntries(for: member)
                    }

                    showEditDiarySheet = false
                    editingDiary = nil

                    // 重新打开历史弹窗
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        showHistorySheet = true
                    }
                } else {
                    errorMessage = error ?? "更新失败"
                    showErrorAlert = true
                }
            }
        }
    }

    /**
     * 删除日记条目
     */
    private func deleteDiaryEntry(_ entry: DiaryEntry) {
        viewModel.deleteDiary(entry) { success, error in
            DispatchQueue.main.async {
                if success {
                    // 刷新历史列表
                    if let member = selectedHistoryMember {
                        loadHistoryDiaryEntries(for: member)
                    }
                } else {
                    errorMessage = error ?? "删除失败"
                    showErrorAlert = true
                }
            }
        }
    }

    /**
     * 查看历史日记
     */
    private func viewHistoryDiary() {
        showHistoryMemberPicker = true
    }

    // MARK: - Draft Content Management

    /**
     * 保存草稿内容到UserDefaults
     */
    private func saveDraftContent() {
        let draftData: [String: Any] = [
            "title": diaryTitle,
            "content": diaryContent,
            "timestamp": selectedDate.timeIntervalSince1970,
            "selectedMemberId": selectedMember?.objectID.uriRepresentation().absoluteString ?? ""
        ]
        UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
    }

    /**
     * 从UserDefaults加载草稿内容
     */
    private func loadDraftContent() {
        guard let draftData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft") else {
            return
        }

        // 恢复标题和内容
        if let title = draftData["title"] as? String {
            diaryTitle = title
        }

        if let content = draftData["content"] as? String {
            diaryContent = content
            // 同步到语音识别服务
            speechRecognitionService.recognizedText = content
        }

        // 恢复日期
        if let timestamp = draftData["timestamp"] as? TimeInterval {
            selectedDate = Date(timeIntervalSince1970: timestamp)
        }

        // 恢复选中的成员（如果存在）
        if let memberIdString = draftData["selectedMemberId"] as? String,
           !memberIdString.isEmpty,
           let memberURL = URL(string: memberIdString) {
            // 通过URL查找对应的成员
            let children = viewModel.getChildren()
            selectedMember = children.first { member in
                member.objectID.uriRepresentation().absoluteString == memberIdString
            }
        }
    }

    /**
     * 清空草稿内容
     */
    private func clearDraftContent() {
        UserDefaults.standard.removeObject(forKey: "GrowthDiaryDraft")
    }
}

// MARK: - Preview
#Preview {
    GrowthDiaryView()
}
