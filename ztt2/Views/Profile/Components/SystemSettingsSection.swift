//
//  SystemSettingsSection.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 设置项类型枚举
 * 移除了iCloudSync选项，因为现在所有用户都自动启用CloudKit同步
 */
enum SettingType: CaseIterable {
    case productIntroduction
    case rateApp
    case feedback
    case about
    case clearAllData

    var displayName: String {
        switch self {
        case .productIntroduction:
            return "settings.item.product_introduction".localized
        case .rateApp:
            return "settings.item.rate_app".localized
        case .feedback:
            return "settings.item.feedback".localized
        case .about:
            return "settings.item.about".localized
        case .clearAllData:
            return "settings.item.clear_all_data".localized
        }
    }

    var iconName: String {
        switch self {
        case .productIntroduction:
            return "产品介绍"
        case .rateApp:
            return "star.fill"
        case .feedback:
            return "guzhangfankui"
        case .about:
            return "guanyu"
        case .clearAllData:
            return "shanchu"
        }
    }

    var isDestructive: Bool {
        switch self {
        case .clearAllData:
            return true
        default:
            return false
        }
    }

    var hasToggle: Bool {
        // 移除了iCloud同步选项，所有设置项都不需要开关
        return false
    }

    /// 检查设置项是否需要付费权限
    var requiresPaidSubscription: Bool {
        // 移除了付费权限限制，所有设置项都可用
        return false
    }

    /// 检查是否为系统图标
    var isSystemIcon: Bool {
        switch self {
        case .rateApp:
            return true
        default:
            return false
        }
    }
}

/**
 * 系统设置组件
 * 包含设置项的列表布局，移除了iCloud同步选项
 */
struct SystemSettingsSection: View {

    // MARK: - Properties
    let onSettingItemPressed: (SettingType) -> Void

    // MARK: - State
    @State private var sectionAppeared = false

    // MARK: - Computed Properties

    /// 获取所有设置项
    private var availableSettings: [SettingType] {
        return SettingType.allCases
    }
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 标题
            HStack {
                Text("settings.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.profileSettingsTextColor)
                
                Spacer()
            }
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .offset(y: sectionAppeared ? 0 : -10)
            .animation(.easeOut(duration: 0.6).delay(0.1), value: sectionAppeared)
            
            // 设置项列表
            VStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                ForEach(availableSettings.indices, id: \.self) { index in
                    let settingType = availableSettings[index]

                    SettingsItemView(
                        settingType: settingType,
                        isToggleEnabled: false, // 移除了toggle功能
                        isDisabled: false, // 所有设置项都可用
                        onTapped: {
                            onSettingItemPressed(settingType)
                        },
                        onToggleChanged: { _ in
                            // 不再需要toggle处理
                        }
                    )
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .offset(y: sectionAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.2 + Double(index) * 0.1), value: sectionAppeared)
                }
            }
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

/**
 * 设置项视图
 */
private struct SettingsItemView: View {

    let settingType: SettingType
    let isToggleEnabled: Bool
    let isDisabled: Bool
    let onTapped: () -> Void
    let onToggleChanged: (Bool) -> Void

    @State private var isPressed = false

    // 默认初始化器，保持向后兼容
    init(settingType: SettingType,
         isToggleEnabled: Bool,
         isDisabled: Bool = false,
         onTapped: @escaping () -> Void,
         onToggleChanged: @escaping (Bool) -> Void) {
        self.settingType = settingType
        self.isToggleEnabled = isToggleEnabled
        self.isDisabled = isDisabled
        self.onTapped = onTapped
        self.onToggleChanged = onToggleChanged
    }
    
    var body: some View {
        Button(action: {
            // 如果是禁用状态，不执行任何操作
            if isDisabled {
                return
            }

            // 如果是开关类型的设置项，不执行点击操作
            if !settingType.hasToggle {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isPressed = true
                    onTapped()
                }

                // 恢复按钮状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    isPressed = false
                }
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                    .fill(Color(hex: "#f6fbe9"))
                    .frame(height: DesignSystem.ProfilePage.SettingsItem.height)
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                            .stroke(Color.white.opacity(0.8), lineWidth: 1)
                    )
                
                // 内容布局
                HStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                    // 左侧图标
                    if settingType.isSystemIcon {
                        Image(systemName: settingType.iconName)
                            .font(.system(
                                size: DesignSystem.ProfilePage.SettingsItem.iconSize,
                                weight: .medium
                            ))
                            .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)
                            .frame(
                                width: DesignSystem.ProfilePage.SettingsItem.iconSize,
                                height: DesignSystem.ProfilePage.SettingsItem.iconSize
                            )
                    } else {
                        Image(settingType.iconName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(
                                width: DesignSystem.ProfilePage.SettingsItem.iconSize,
                                height: DesignSystem.ProfilePage.SettingsItem.iconSize
                            )
                            .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)
                    }
                    
                    // 设置项文字
                    HStack(spacing: 8) {
                        Text(settingType.displayName)
                            .font(.system(
                                size: DesignSystem.ProfilePage.SettingsItem.textFont,
                                weight: .medium
                            ))
                            .foregroundColor(isDisabled ? Color(hex: "#C0C0C0") : Color(hex: "#808080"))
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)

                        // 如果是禁用状态，显示会员标识
                        if isDisabled && settingType.requiresPaidSubscription {
                            Text("feature.premium_required".localized)
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange)
                                .cornerRadius(4)
                        }
                    }
                    
                    Spacer()

                    // 右侧控件：开关或箭头
                    if settingType.hasToggle {
                        Toggle("", isOn: Binding(
                            get: { isToggleEnabled },
                            set: { newValue in
                                if !isDisabled {
                                    onToggleChanged(newValue)
                                }
                            }
                        ))
                        .toggleStyle(SwitchToggleStyle(tint: isDisabled ? Color.gray : Color(hex: "#8BC34A")))
                        .scaleEffect(0.8)
                        .disabled(isDisabled)
                    } else {
                        // 右侧箭头
                        Image(systemName: "chevron.right")
                            .font(.system(
                                size: DesignSystem.ProfilePage.SettingsItem.arrowSize,
                                weight: .medium
                            ))
                            .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
                    }
                }
                .padding(.horizontal, DesignSystem.ProfilePage.SettingsItem.padding)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    SystemSettingsSection(
        onSettingItemPressed: { settingType in
            print("设置项被点击: \(settingType.displayName)")
        }
    )
    .padding()
    .background(DesignSystem.Colors.background)
}
