//
//  SubscriptionView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 订阅页面主视图
 * 实现三层ZStack架构：独立组件层(最底层) -> 背景图层(中间层) -> 分段选项卡层(最上层)
 * 支持响应式布局，适配不同设备尺寸
 */
struct SubscriptionView: View {
    
    // MARK: - Properties
    var onDismiss: (() -> Void)? = nil
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss

    // MARK: - State Objects
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var subscriptionService = SubscriptionService.shared

    // MARK: - State
    @State private var selectedMembershipType: Int = 0 // 0: 初级会员, 1: 高级会员
    @State private var selectedPriceType: Int = 0 // 0: 月会员, 1: 年会员
    @State private var agreementAccepted: Bool = false
    @State private var showSuccessModal: Bool = false // 订阅成功弹窗状态
    @State private var isLoading: Bool = false
    @State private var purchaseSuccess: Bool = false
    @State private var errorMessage: String? = nil

    // MARK: - 分层动画状态管理
    @State private var userInfoAppeared: Bool = false
    @State private var contentAppeared: Bool = false
    @State private var tabAppeared: Bool = false
    @State private var pageAppeared: Bool = false // 保留原有状态以兼容现有逻辑

    // MARK: - Computed Properties
    // 从DataManager获取真实用户数据，与ProfileView保持一致
    private var userName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }

    private var userID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }

    private var membershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }

    private var expirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    var body: some View {
        GeometryReader { geometry in
            setupGeometry(geometry: geometry)
        }
        .onAppear {
            setupEntranceAnimations()
            loadUserData()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置几何布局
     * 实现与ztt1完全一致的绝对定位系统
     */
    private func setupGeometry(geometry: GeometryProxy) -> some View {
        let screenWidth = geometry.size.width
        let screenHeight = geometry.size.height

        return ZStack {
            // 底层：屏幕背景色
            Color(hex: "#fcfff4")
                .ignoresSafeArea(.all)

            // 第一层：独立组件层
            ZStack {
                // 个人信息组件 - 绝对定位到顶部30%
                SubscriptionUserInfoSection(
                    userName: userName,
                    userID: userID,
                    membershipLevel: membershipLevel,
                    expirationDate: expirationDate
                ) {
                    handleBackPressed()
                }
                .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.heightPercentage)
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.UserInfoSection.topPositionPercentage
                )
                .opacity(userInfoAppeared ? 1.0 : 0.0)
                .offset(y: userInfoAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.offsetY)
                .scaleEffect(userInfoAppeared ? DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.scaleEnd : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.scaleStart)
                .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.delay), value: userInfoAppeared)

                // 会员内容组件 - 绝对定位到下方
                MembershipContentView(
                    selectedMembershipType: $selectedMembershipType,
                    selectedPriceType: $selectedPriceType,
                    agreementAccepted: $agreementAccepted,
                    isLoading: isLoading,
                    purchaseSuccess: purchaseSuccess,
                    errorMessage: errorMessage
                ) {
                    handleSubscribePressed()
                }
                .position(
                    x: screenWidth / 2,
                    y: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.ContentSection.topOffsetPercentage + 120
                )
                .opacity(contentAppeared ? 1.0 : 0.0)
                .offset(y: contentAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.offsetY)
                .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.delay), value: contentAppeared)
            }

            // 第二层：背景图层（中间层，覆盖在独立组件之上）
            BackgroundImageLayer(selectedMembershipType: $selectedMembershipType)

            // 第三层：分段选项卡层（最上层，透明背景）
            VStack {
                Spacer()
                    .frame(height: screenHeight * DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.topOffsetPercentage)

                MembershipTabSegment(selectedTab: $selectedMembershipType) { newTab in
                    handleMembershipTypeChanged(newTab: newTab)
                }
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
                .padding(.horizontal, DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.horizontalPadding) // 使用可调整的水平边距
                .opacity(tabAppeared ? 1.0 : 0.0)
                .scaleEffect(tabAppeared ? DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.scaleEnd : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.scaleStart)
                .offset(y: tabAppeared ? 0 : DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.offsetY)
                .animation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.delay), value: tabAppeared)

                Spacer()
            }
        }
        .ignoresSafeArea(.all, edges: [.top, .leading, .trailing])
    }
    

    
    /**
     * 设置入场动画序列
     * 实现与ztt1一致的动画效果
     */
    private func setupEntranceAnimations() {
        // 重置所有动画状态
        resetAnimationStates()

        // 分层启动动画序列
        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.UserInfo.delay)) {
            userInfoAppeared = true
        }

        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Content.delay)) {
            contentAppeared = true
        }

        withAnimation(.spring(response: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.duration, dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.springDamping).delay(DesignSystem.AdaptiveLayout.SubscriptionPage.AnimationPresets.Tab.delay)) {
            tabAppeared = true
        }

        // 保持原有的pageAppeared状态兼容性
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
            pageAppeared = true
        }
    }

    /**
     * 重置动画状态
     */
    private func resetAnimationStates() {
        userInfoAppeared = false
        contentAppeared = false
        tabAppeared = false
        pageAppeared = false
    }

    /**
     * 处理返回按钮点击
     */
    private func handleBackPressed() {
        print("🔙 返回按钮被点击")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // 调用回调或使用环境dismiss
        if let onDismiss = onDismiss {
            onDismiss()
        } else {
            dismiss()
        }
    }

    /**
     * 处理分段选项卡切换
     */
    private func handleMembershipTypeChanged(newTab: Int) {
        let membershipTypeName = newTab == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        print("会员类型切换到: \(membershipTypeName)")

        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            // selectedMembershipType 通过 Binding 自动更新
            // 重置价格选择为月会员
            selectedPriceType = 0
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理订阅按钮点击
     */
    private func handleSubscribePressed() {
        let membershipTypeName = selectedMembershipType == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized
        let priceTypeName = selectedPriceType == 0 ? "subscription.pricing.monthly".localized : "subscription.pricing.yearly".localized

        print("🛒 订阅按钮被点击 - \(membershipTypeName) \(priceTypeName)")

        // 重置购买状态
        resetPurchaseState()

        // 确定产品ID
        let productId = getProductId(
            membershipType: selectedMembershipType,
            priceType: selectedPriceType
        )

        print("🆔 准备购买产品: \(productId)")

        // 开始购买流程
        Task {
            await purchaseSubscription(productId: productId)
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }



    /**
     * 重置购买状态
     */
    private func resetPurchaseState() {
        isLoading = false
        purchaseSuccess = false
        errorMessage = nil
    }

    /**
     * 根据用户选择获取产品ID
     */
    private func getProductId(membershipType: Int, priceType: Int) -> String {
        switch (membershipType, priceType) {
        case (0, 0): // 初级会员 + 月度
            return "com.ztt2.subscription.monthly.basic"
        case (0, 1): // 初级会员 + 年度
            return "com.ztt2.subscription.yearly.basic"
        case (1, 0): // 高级会员 + 月度
            return "com.ztt2.subscription.monthly.premium"
        case (1, 1): // 高级会员 + 年度
            return "com.ztt2.subscription.yearly.premium"
        default:
            return "com.ztt2.subscription.monthly.basic" // 默认为初级月度
        }
    }

    /**
     * 执行订阅购买
     */
    private func purchaseSubscription(productId: String) async {
        // 重置状态
        await MainActor.run {
            isLoading = true
            errorMessage = ""
            purchaseSuccess = false
        }

        print("🛒 开始购买订阅: \(productId)")

        // 调用RevenueCat进行真实购买
        let success = await subscriptionService.purchaseProduct(productId: productId)

        await MainActor.run {
            isLoading = false

            if success {
                // 购买成功
                print("✅ 订阅成功: \(productId)")
                purchaseSuccess = true

                // 成功反馈
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)

                // 显示订阅成功弹窗
                showSuccessModal = true

                // 刷新用户数据
                dataManager.refreshCurrentUser()

            } else {
                // 购买失败
                print("❌ 订阅失败")
                errorMessage = subscriptionService.errorMessage ?? "purchase.error.general".localized

                // 错误反馈
                let errorFeedback = UINotificationFeedbackGenerator()
                errorFeedback.notificationOccurred(.error)
            }
        }
    }



    /**
     * 加载用户数据
     * 在页面出现时调用，用于加载和刷新用户信息
     */
    private func loadUserData() {
        // 刷新用户数据，确保显示最新的订阅状态
        dataManager.refreshCurrentUser()

        print("📱 订阅页面加载用户数据：\(userName)")
        print("📱 当前会员等级：\(membershipLevel)")
        print("📱 到期时间：\(expirationDate)")
    }
}

// MARK: - Preview
#Preview {
    SubscriptionView()
        .preferredColorScheme(.light)
}
