//
//  UserInfoComparisonView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/4.
//

import SwiftUI

/**
 * 用户信息对比视图
 * 并排显示个人中心和订阅页面的用户信息，便于直观对比
 */
struct UserInfoComparisonView: View {
    
    // MARK: - State Objects
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题说明
                    VStack(spacing: 10) {
                        Text("用户信息一致性对比")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("验证个人中心页面和订阅页面显示的用户信息是否一致")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // 对比卡片
                    HStack(spacing: 15) {
                        // 个人中心信息
                        UserInfoCard(
                            title: "个人中心页面",
                            userName: profileUserName,
                            userID: profileUserID,
                            membershipLevel: profileMembershipLevel,
                            expirationDate: profileExpirationDate,
                            cardColor: .blue
                        )
                        
                        // 订阅页面信息
                        UserInfoCard(
                            title: "订阅页面",
                            userName: subscriptionUserName,
                            userID: subscriptionUserID,
                            membershipLevel: subscriptionMembershipLevel,
                            expirationDate: subscriptionExpirationDate,
                            cardColor: .green
                        )
                    }
                    .padding(.horizontal)
                    
                    // 一致性检查结果
                    ConsistencyCheckView(
                        userNameMatch: profileUserName == subscriptionUserName,
                        userIDMatch: profileUserID == subscriptionUserID,
                        membershipLevelMatch: profileMembershipLevel == subscriptionMembershipLevel,
                        expirationDateMatch: profileExpirationDate == subscriptionExpirationDate
                    )
                    .padding()
                    
                    // 刷新按钮
                    Button(action: refreshData) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("刷新数据")
                        }
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(10)
                    }
                    .padding()
                    
                    Spacer()
                }
            }
            .navigationTitle("信息一致性检查")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            refreshData()
        }
    }
    
    // MARK: - Computed Properties (模拟ProfileView的逻辑)
    
    private var profileUserName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private var profileUserID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private var profileMembershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private var profileExpirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    // MARK: - Computed Properties (模拟SubscriptionView的逻辑)
    
    private var subscriptionUserName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private var subscriptionUserID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private var subscriptionMembershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private var subscriptionExpirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    // MARK: - Private Methods
    
    private func refreshData() {
        dataManager.refreshCurrentUser()
        print("🔄 刷新用户数据完成")
    }
}

// MARK: - Supporting Views

/**
 * 用户信息卡片
 */
struct UserInfoCard: View {
    let title: String
    let userName: String
    let userID: String
    let membershipLevel: String
    let expirationDate: String
    let cardColor: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 卡片标题
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(cardColor)
            
            Divider()
            
            // 用户信息
            VStack(alignment: .leading, spacing: 8) {
                InfoRow(label: "用户名", value: userName)
                InfoRow(label: "用户ID", value: userID)
                InfoRow(label: "会员等级", value: membershipLevel)
                InfoRow(label: "到期日期", value: expirationDate)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(cardColor.opacity(0.3), lineWidth: 2)
        )
    }
}

/**
 * 信息行
 */
struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.body)
                .fontWeight(.medium)
        }
    }
}

/**
 * 一致性检查视图
 */
struct ConsistencyCheckView: View {
    let userNameMatch: Bool
    let userIDMatch: Bool
    let membershipLevelMatch: Bool
    let expirationDateMatch: Bool
    
    private var allMatch: Bool {
        userNameMatch && userIDMatch && membershipLevelMatch && expirationDateMatch
    }
    
    var body: some View {
        VStack(spacing: 15) {
            // 总体状态
            HStack {
                Image(systemName: allMatch ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                    .foregroundColor(allMatch ? .green : .orange)
                    .font(.title2)
                
                Text(allMatch ? "✅ 所有信息一致" : "⚠️ 发现不一致")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(allMatch ? .green : .orange)
                
                Spacer()
            }
            
            // 详细检查结果
            VStack(spacing: 8) {
                CheckResultRow(label: "用户名", isMatch: userNameMatch)
                CheckResultRow(label: "用户ID", isMatch: userIDMatch)
                CheckResultRow(label: "会员等级", isMatch: membershipLevelMatch)
                CheckResultRow(label: "到期日期", isMatch: expirationDateMatch)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

/**
 * 检查结果行
 */
struct CheckResultRow: View {
    let label: String
    let isMatch: Bool
    
    var body: some View {
        HStack {
            Image(systemName: isMatch ? "checkmark.circle" : "xmark.circle")
                .foregroundColor(isMatch ? .green : .red)
            
            Text(label)
                .font(.body)
            
            Spacer()
            
            Text(isMatch ? "一致" : "不一致")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isMatch ? .green : .red)
        }
    }
}

// MARK: - Preview

#Preview {
    UserInfoComparisonView()
}
