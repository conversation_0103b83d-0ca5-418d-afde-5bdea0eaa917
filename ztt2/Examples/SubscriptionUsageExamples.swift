//
//  SubscriptionUsageExamples.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import SwiftUI

/**
 * 订阅权限使用示例
 * 展示如何在应用的各个功能中集成订阅权限检查
 */

// MARK: - 示例1：AI分析功能权限检查

struct AIAnalysisButtonExample: View {
    
    var body: some View {
        Button("生成AI分析报告") {
            checkAIAnalysisPermission()
        }
        .foregroundColor(canUseAIAnalysis() ? .blue : .gray)
        .disabled(!canUseAIAnalysis())
    }
    
    private func canUseAIAnalysis() -> Bool {
        return SubscriptionPermissionManager.shared.canUseAIAnalysis()
    }
    
    private func checkAIAnalysisPermission() {
        if canUseAIAnalysis() {
            // 执行AI分析
            generateAIAnalysis()
        } else {
            // 显示权限不足提示
            showUpgradePrompt(for: .aiAnalysis)
        }
    }
    
    private func generateAIAnalysis() {
        // AI分析逻辑
        print("开始生成AI分析报告")
    }
    
    private func showUpgradePrompt(for feature: SubscriptionFeature) {
        // 显示升级提示
        SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: feature)
    }
}

// MARK: - 示例2：抽奖功能权限检查

struct LotteryOptionsExample: View {
    
    var body: some View {
        VStack(spacing: 16) {
            // 大转盘（初级会员及以上）
            LotteryOptionCard(
                title: "大转盘",
                description: "经典转盘抽奖",
                isAvailable: canUseBasicLottery(),
                requiredLevel: "初级会员"
            ) {
                checkBasicLotteryPermission()
            }
            
            // 盲盒（高级会员）
            LotteryOptionCard(
                title: "盲盒",
                description: "神秘盲盒抽奖",
                isAvailable: canUseAdvancedLottery(),
                requiredLevel: "高级会员"
            ) {
                checkAdvancedLotteryPermission()
            }
            
            // 刮刮卡（高级会员）
            LotteryOptionCard(
                title: "刮刮卡",
                description: "刮开惊喜奖品",
                isAvailable: canUseAdvancedLottery(),
                requiredLevel: "高级会员"
            ) {
                checkAdvancedLotteryPermission()
            }
        }
    }
    
    private func canUseBasicLottery() -> Bool {
        return SubscriptionPermissionManager.shared.canUseBasicLottery()
    }
    
    private func canUseAdvancedLottery() -> Bool {
        return SubscriptionPermissionManager.shared.canUseAdvancedLottery()
    }
    
    private func checkBasicLotteryPermission() {
        if canUseBasicLottery() {
            openWheelLottery()
        } else {
            SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: .basicLottery)
        }
    }
    
    private func checkAdvancedLotteryPermission() {
        if canUseAdvancedLottery() {
            openAdvancedLottery()
        } else {
            SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: .advancedLottery)
        }
    }
    
    private func openWheelLottery() {
        print("打开大转盘")
    }
    
    private func openAdvancedLottery() {
        print("打开高级抽奖")
    }
}

struct LotteryOptionCard: View {
    let title: String
    let description: String
    let isAvailable: Bool
    let requiredLevel: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(isAvailable ? .primary : .gray)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if !isAvailable {
                        Text("需要\(requiredLevel)")
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                }
                
                Spacer()
                
                Image(systemName: isAvailable ? "checkmark.circle.fill" : "lock.circle.fill")
                    .foregroundColor(isAvailable ? .green : .gray)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .disabled(!isAvailable)
    }
}

// MARK: - 示例3：成员创建限制检查

struct AddMemberButtonExample: View {
    @State private var currentMemberCount: Int = 5 // 示例：当前已有5个成员
    
    var body: some View {
        VStack(spacing: 8) {
            Button("添加家庭成员") {
                addNewMember() // 直接添加成员，无需检查限制
            }

            // 显示成员数量（无限制）
            Text("成员数量：\(currentMemberCount)（无限制）")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // 移除成员数量限制相关的方法，现在所有用户都可以创建无限成员
    
    private func addNewMember() {
        // 添加成员逻辑（无限制）
        currentMemberCount += 1
        print("添加新成员，当前成员数：\(currentMemberCount)（无限制）")
    }
}

// MARK: - 示例4：订阅状态显示

struct SubscriptionStatusExample: View {
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("订阅状态")
                .font(.headline)
            
            HStack {
                Image(systemName: isPaidUser() ? "crown.fill" : "person.circle")
                    .foregroundColor(isPaidUser() ? .yellow : .gray)
                
                VStack(alignment: .leading) {
                    Text(getCurrentSubscriptionDescription())
                        .font(.subheadline)
                    
                    if !isPaidUser() {
                        Button("升级会员") {
                            showSubscriptionView()
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }
            
            // 功能权限列表
            VStack(alignment: .leading, spacing: 4) {
                PermissionRow(title: "多设备同步", hasPermission: canUseCloudSync())
                PermissionRow(title: "AI分析", hasPermission: canUseAIAnalysis())
                PermissionRow(title: "大转盘抽奖", hasPermission: canUseBasicLottery())
                PermissionRow(title: "盲盒&刮刮卡", hasPermission: canUseAdvancedLottery())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func isPaidUser() -> Bool {
        return SubscriptionPermissionManager.shared.isPaidUser()
    }
    
    private func getCurrentSubscriptionDescription() -> String {
        return SubscriptionPermissionManager.shared.getCurrentSubscriptionDescription()
    }
    
    private func canUseCloudSync() -> Bool {
        return SubscriptionPermissionManager.shared.canUseCloudSync()
    }
    
    private func canUseAIAnalysis() -> Bool {
        return SubscriptionPermissionManager.shared.canUseAIAnalysis()
    }
    
    private func canUseBasicLottery() -> Bool {
        return SubscriptionPermissionManager.shared.canUseBasicLottery()
    }
    
    private func canUseAdvancedLottery() -> Bool {
        return SubscriptionPermissionManager.shared.canUseAdvancedLottery()
    }
    
    private func showSubscriptionView() {
        // 显示订阅页面
        NotificationCenter.default.post(name: .showSubscriptionView, object: nil)
    }
}

struct PermissionRow: View {
    let title: String
    let hasPermission: Bool
    
    var body: some View {
        HStack {
            Image(systemName: hasPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(hasPermission ? .green : .red)
                .font(.caption)
            
            Text(title)
                .font(.caption)
                .foregroundColor(hasPermission ? .primary : .secondary)
            
            Spacer()
        }
    }
}

// MARK: - 预览

#Preview {
    VStack(spacing: 20) {
        AIAnalysisButtonExample()
        AddMemberButtonExample()
        SubscriptionStatusExample()
    }
    .padding()
}
