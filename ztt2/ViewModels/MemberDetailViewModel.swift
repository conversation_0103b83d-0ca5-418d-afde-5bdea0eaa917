//
//  MemberDetailViewModel.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import SwiftUI
import Combine
import CoreData

/**
 * 成员详情页视图模型
 * 管理成员详情页的数据和业务逻辑
 */
@MainActor
class MemberDetailViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前成员
    @Published var member: Member?
    
    /// 积分记录列表
    @Published var pointRecords: [PointRecord] = []
    
    /// 兑换记录列表（包含抽奖记录）
    @Published var redemptionRecords: [ExchangeRecord] = []
    
    /// 加分规则列表
    @Published var addPointsRules: [MemberRule] = []
    
    /// 扣分规则列表
    @Published var deductPointsRules: [MemberRule] = []
    
    /// 奖品列表
    @Published var memberPrizes: [MemberPrize] = []
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    private let memberId: String
    
    // MARK: - Initialization
    
    init(memberId: String) {
        self.memberId = memberId
        setupDataBinding()
        loadMemberData()
    }
    
    // MARK: - Data Binding
    
    private func setupDataBinding() {
        // 监听DataManager中的成员变化
        dataManager.$members
            .receive(on: DispatchQueue.main)
            .sink { [weak self] members in
                self?.updateMemberData(from: members)
            }
            .store(in: &cancellables)
    }
    
    private func updateMemberData(from members: [Member]) {
        // 根据memberId找到对应的成员
        if let foundMember = members.first(where: { 
            $0.objectID.uriRepresentation().absoluteString == memberId 
        }) {
            member = foundMember
            loadMemberRelatedData()
        }
    }
    
    // MARK: - Data Loading
    
    private func loadMemberData() {
        isLoading = true
        errorMessage = nil
        
        // 从DataManager获取成员数据
        if let foundMember = dataManager.members.first(where: { 
            $0.objectID.uriRepresentation().absoluteString == memberId 
        }) {
            member = foundMember
            loadMemberRelatedData()
        } else {
            errorMessage = "未找到指定成员"
        }
        
        isLoading = false
    }
    
    private func loadMemberRelatedData() {
        guard let member = member else { return }
        
        // 加载积分记录（只显示行为记录，排除兑换和抽奖记录）
        pointRecords = member.behaviorPointRecords
        
        // 加载兑换记录（合并兑换记录和抽奖记录）
        loadExchangeRecords()
        
        // 加载规则
        loadMemberRules()
        
        // 加载奖品
        memberPrizes = member.allMemberPrizes
    }
    
    private func loadMemberRules() {
        guard let member = member else { return }

        let allRules = member.allMemberRules

        // 分离加分和扣分规则
        addPointsRules = allRules.filter { ($0.type ?? "") == "add" }
        deductPointsRules = allRules.filter { ($0.type ?? "") == "deduct" }
    }

    private func loadExchangeRecords() {
        guard let member = member else { return }

        var exchangeRecords: [ExchangeRecord] = []

        // 添加兑换记录
        let redemptionRecords = member.allRedemptionRecords
        for record in redemptionRecords {
            let exchangeRecord = ExchangeRecord(
                id: record.id ?? UUID(),
                prizeName: record.prizeName ?? "未知奖品",
                cost: record.cost,
                timestamp: record.timestamp ?? Date(),
                source: record.source ?? "redemption",
                recordType: .redemption(record)
            )
            exchangeRecords.append(exchangeRecord)
        }

        // 添加抽奖记录
        let lotteryRecords = member.allLotteryRecords
        for record in lotteryRecords {
            let exchangeRecord = ExchangeRecord(
                id: record.id ?? UUID(),
                prizeName: record.prizeResult ?? "未知奖品",
                cost: record.cost,
                timestamp: record.timestamp ?? Date(),
                source: record.toolType ?? "lottery",
                recordType: .lottery(record)
            )
            exchangeRecords.append(exchangeRecord)
        }

        // 按时间倒序排序
        self.redemptionRecords = exchangeRecords.sorted {
            $0.timestamp > $1.timestamp
        }
    }
    
    // MARK: - Public Methods
    
    /**
     * 刷新数据
     */
    func refresh() {
        loadMemberData()
    }
    
    /**
     * 添加积分记录
     */
    func addPointRecord(reason: String, value: Int) {
        guard let member = member else { return }
        
        isLoading = true
        errorMessage = nil
        
        dataManager.addPointRecord(to: member, reason: reason, value: Int32(value))
        
        isLoading = false
        print("添加积分记录: \(reason), 分值: \(value)")
    }
    
    /**
     * 批量添加积分记录
     */
    func addMultiplePointRecords(items: [FormItem], saveAsRule: Bool = false) {
        guard let member = member else { return }

        isLoading = true
        errorMessage = nil

        for item in items {
            let value = item.operationType == .add ? item.value : -item.value
            dataManager.addPointRecord(to: member, reason: item.reason, value: Int32(value))

            // 如果该项选择保存为规则
            if item.saveAsRule {
                let ruleType = item.operationType == .add ? "add" : "deduct"
                let _ = dataManager.createMemberRule(
                    for: member,
                    name: item.reason,
                    value: Int32(abs(item.value)),
                    type: ruleType,
                    isFrequent: true
                )
            }
        }

        isLoading = false
        print("批量添加积分记录: \(items.count) 项")
    }
    
    /**
     * 只添加规则，不执行积分操作
     */
    func addRulesOnly(items: [FormItem]) {
        guard let member = member else { return }

        isLoading = true
        errorMessage = nil

        for item in items {
            let ruleType = item.operationType == .add ? "add" : "deduct"
            let _ = dataManager.createMemberRule(
                for: member,
                name: item.reason,
                value: Int32(abs(item.value)),
                type: ruleType,
                isFrequent: true
            )
        }

        isLoading = false
        print("只添加规则: \(items.count) 项规则")
    }

    /**
     * 创建成员规则
     */
    func createMemberRule(name: String, value: Int, type: String) {
        guard let member = member else { return }

        isLoading = true
        errorMessage = nil

        let _ = dataManager.createMemberRule(
            for: member,
            name: name,
            value: Int32(value),
            type: type,
            isFrequent: true
        )

        isLoading = false
        print("创建成员规则: \(name), 分值: \(value), 类型: \(type)")
    }
    
    /**
     * 删除成员规则
     */
    func deleteMemberRule(_ rule: MemberRule) {
        isLoading = true
        errorMessage = nil
        
        dataManager.deleteMemberRule(rule)
        
        isLoading = false
        print("删除成员规则: \(rule.name ?? "未知规则")")
    }
    
    /**
     * 创建成员奖品
     */
    func createMemberPrize(name: String, cost: Int, description: String? = nil) {
        guard let member = member else { return }
        
        isLoading = true
        errorMessage = nil
        
        let _ = dataManager.createMemberPrize(
            for: member,
            name: name,
            cost: Int32(cost),
            type: description
        )
        
        isLoading = false
        print("创建成员奖品: \(name), 消耗积分: \(cost)")
    }
    
    /**
     * 删除成员奖品
     */
    func deleteMemberPrize(_ prize: MemberPrize) {
        isLoading = true
        errorMessage = nil
        
        dataManager.deleteMemberPrize(prize)
        
        isLoading = false
        print("删除成员奖品: \(prize.name ?? "未知奖品")")
    }
    
    /**
     * 兑换奖品
     */
    func redeemPrize(name: String, cost: Int) {
        guard let member = member else { return }
        
        // 检查积分是否足够
        guard member.currentPoints >= cost else {
            errorMessage = "积分不足，无法兑换"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        // 直接扣除积分，不创建积分记录
        member.currentPoints -= Int32(cost)
        member.updatedAt = Date()

        // 创建兑换记录（内部会自动保存）
        dataManager.createRedemptionRecord(for: member, prizeName: name, cost: Int32(cost))
        
        isLoading = false
        print("兑换奖品: \(name), 消耗积分: \(cost)")
    }
    
    /**
     * 撤销积分记录
     */
    func reversePointRecord(_ record: PointRecord) {
        isLoading = true
        errorMessage = nil

        dataManager.reversePointRecord(record)

        isLoading = false
        print("撤销积分记录: \(record.reason ?? "未知原因")")
    }

    /**
     * 删除积分记录
     */
    func deletePointRecord(_ record: PointRecord) {
        guard let member = member else { return }

        isLoading = true
        errorMessage = nil

        // 在删除前保存记录信息用于日志
        let recordReason = record.reason ?? "未知原因"
        let recordValue = record.value

        // 检查是否已经撤销
        if record.isReversed {
            // 如果已撤销，直接删除记录
            dataManager.deletePointRecord(record)
        } else {
            // 如果未撤销，需要先回滚积分再删除
            let rollbackValue = -record.value
            let newPoints = member.currentPoints + rollbackValue

            // 检查回滚后积分是否为负
            if newPoints < 0 {
                errorMessage = "删除失败：回滚后积分将为负数"
                isLoading = false
                return
            }

            // 回滚积分并删除记录
            member.currentPoints = newPoints
            member.updatedAt = Date()
            dataManager.deletePointRecord(record)
        }

        isLoading = false
        print("删除积分记录: \(recordReason) (积分值: \(recordValue))")
    }

    /**
     * 删除兑换记录
     */
    func deleteExchangeRecord(_ record: ExchangeRecord) {
        guard let member = member else { return }

        isLoading = true
        errorMessage = nil

        // 在删除前保存记录信息用于日志
        let prizeName = record.prizeName
        let cost = record.cost
        let source = record.source

        // 返还消耗的积分
        member.currentPoints += record.cost
        member.updatedAt = Date()

        // 根据记录类型删除对应的记录
        switch record.recordType {
        case .redemption(let redemptionRecord):
            dataManager.deleteRedemptionRecord(redemptionRecord)
        case .lottery(let lotteryRecord):
            dataManager.deleteLotteryRecord(lotteryRecord)
        }

        isLoading = false
        print("删除兑换记录: \(prizeName) (消耗积分: \(cost), 来源: \(source))")
    }
}

// MARK: - Supporting Types

extension MemberDetailViewModel {

    /// 表单项数据结构
    struct FormItem {
        let reason: String
        let value: Int
        let operationType: MemberPointsOperationType
        let saveAsRule: Bool
    }

    /// 统一的兑换记录数据结构（包含兑换记录和抽奖记录）
    struct ExchangeRecord {
        let id: UUID
        let prizeName: String
        let cost: Int32
        let timestamp: Date
        let source: String
        let recordType: ExchangeRecordType

        enum ExchangeRecordType {
            case redemption(RedemptionRecord)
            case lottery(LotteryRecord)
        }

        /// 获取来源显示名称
        var sourceDisplayName: String {
            switch recordType {
            case .redemption:
                return "兑换"
            case .lottery(let lotteryRecord):
                switch lotteryRecord.toolType {
                case "wheel":
                    return "大转盘"
                case "blindbox":
                    return "盲盒"
                case "scratchcard":
                    return "刮刮卡"
                default:
                    return "抽奖"
                }
            }
        }

        /// 获取来源颜色
        var sourceColor: String {
            switch recordType {
            case .redemption:
                return "#a9d051" // 绿色系，表示直接兑换
            case .lottery(let lotteryRecord):
                switch lotteryRecord.toolType {
                case "wheel":
                    return "#FF6B6B" // 红色系，表示大转盘抽奖
                case "blindbox":
                    return "#9013FE" // 紫色系，表示盲盒抽奖
                case "scratchcard":
                    return "#50E3C2" // 青色系，表示刮刮卡抽奖
                default:
                    return "#FF6B6B"
                }
            }
        }
    }
}

// MARK: - Computed Properties

extension MemberDetailViewModel {
    
    /// 当前积分
    var currentPoints: Int {
        return Int(member?.currentPoints ?? 0)
    }
    
    /// 成员姓名
    var memberName: String {
        return member?.displayName ?? "未知成员"
    }
    
    /// 成员角色
    var memberRole: String {
        return member?.roleDisplayName ?? "未知角色"
    }
    
    /// 成员年龄
    var memberAge: Int {
        return member?.age ?? 0
    }
    
    /// 是否为孩子角色
    var isChild: Bool {
        return member?.isChild ?? false
    }
    
    /// 可以生成AI分析报告
    var canGenerateAnalysisReport: Bool {
        return member?.canGenerateAnalysisReport ?? false
    }
    
    /// 可以生成成长报告
    var canGenerateGrowthReport: Bool {
        return member?.canGenerateGrowthReport ?? false
    }
}
