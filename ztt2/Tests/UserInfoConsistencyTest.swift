//
//  UserInfoConsistencyTest.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/4.
//

import SwiftUI

/**
 * 用户信息一致性测试视图
 * 用于验证订阅页面和个人中心页面的个人信息显示是否一致
 */
struct UserInfoConsistencyTest: View {
    
    // MARK: - State Objects
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    
    // MARK: - State
    @State private var testResults: [UserInfoTestResult] = []
    @State private var isTestRunning = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("用户信息一致性测试")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding()
                
                // 测试按钮
                Button(action: runConsistencyTest) {
                    HStack {
                        if isTestRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.circle")
                        }
                        Text(isTestRunning ? "测试中..." : "运行一致性测试")
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(isTestRunning ? Color.gray : Color.blue)
                    .cornerRadius(10)
                }
                .disabled(isTestRunning)
                
                // 测试结果
                if !testResults.isEmpty {
                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 10) {
                            ForEach(testResults, id: \.id) { result in
                                UserInfoTestResultRow(result: result)
                            }
                        }
                        .padding()
                    }
                }
                
                Spacer()
            }
            .navigationTitle("一致性测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 运行一致性测试
     */
    private func runConsistencyTest() {
        isTestRunning = true
        testResults.removeAll()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            performTests()
            isTestRunning = false
        }
    }
    
    /**
     * 执行具体测试
     */
    private func performTests() {
        // 测试用户名一致性
        testUserNameConsistency()
        
        // 测试用户ID一致性
        testUserIDConsistency()
        
        // 测试会员等级一致性
        testMembershipLevelConsistency()
        
        // 测试到期日期一致性
        testExpirationDateConsistency()
    }
    
    /**
     * 测试用户名一致性
     */
    private func testUserNameConsistency() {
        let profileUserName = getProfileUserName()
        let subscriptionUserName = getSubscriptionUserName()
        
        let isConsistent = profileUserName == subscriptionUserName
        
        testResults.append(UserInfoTestResult(
            id: UUID(),
            testName: "用户名一致性",
            profileValue: profileUserName,
            subscriptionValue: subscriptionUserName,
            isConsistent: isConsistent,
            description: isConsistent ? "✅ 用户名显示一致" : "❌ 用户名显示不一致"
        ))
    }
    
    /**
     * 测试用户ID一致性
     */
    private func testUserIDConsistency() {
        let profileUserID = getProfileUserID()
        let subscriptionUserID = getSubscriptionUserID()
        
        let isConsistent = profileUserID == subscriptionUserID
        
        testResults.append(UserInfoTestResult(
            id: UUID(),
            testName: "用户ID一致性",
            profileValue: profileUserID,
            subscriptionValue: subscriptionUserID,
            isConsistent: isConsistent,
            description: isConsistent ? "✅ 用户ID显示一致" : "❌ 用户ID显示不一致"
        ))
    }
    
    /**
     * 测试会员等级一致性
     */
    private func testMembershipLevelConsistency() {
        let profileLevel = getProfileMembershipLevel()
        let subscriptionLevel = getSubscriptionMembershipLevel()
        
        let isConsistent = profileLevel == subscriptionLevel
        
        testResults.append(UserInfoTestResult(
            id: UUID(),
            testName: "会员等级一致性",
            profileValue: profileLevel,
            subscriptionValue: subscriptionLevel,
            isConsistent: isConsistent,
            description: isConsistent ? "✅ 会员等级显示一致" : "❌ 会员等级显示不一致"
        ))
    }
    
    /**
     * 测试到期日期一致性
     */
    private func testExpirationDateConsistency() {
        let profileDate = getProfileExpirationDate()
        let subscriptionDate = getSubscriptionExpirationDate()
        
        let isConsistent = profileDate == subscriptionDate
        
        testResults.append(UserInfoTestResult(
            id: UUID(),
            testName: "到期日期一致性",
            profileValue: profileDate,
            subscriptionValue: subscriptionDate,
            isConsistent: isConsistent,
            description: isConsistent ? "✅ 到期日期显示一致" : "❌ 到期日期显示不一致"
        ))
    }
    
    // MARK: - Helper Methods (模拟ProfileView和SubscriptionView的逻辑)
    
    private func getProfileUserName() -> String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private func getSubscriptionUserName() -> String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private func getProfileUserID() -> String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private func getSubscriptionUserID() -> String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private func getProfileMembershipLevel() -> String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private func getSubscriptionMembershipLevel() -> String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType ?? "free" {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private func getProfileExpirationDate() -> String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    private func getSubscriptionExpirationDate() -> String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
}

// MARK: - Supporting Types

/**
 * 用户信息测试结果数据模型
 */
struct UserInfoTestResult {
    let id: UUID
    let testName: String
    let profileValue: String
    let subscriptionValue: String
    let isConsistent: Bool
    let description: String
}

/**
 * 用户信息测试结果行视图
 */
struct UserInfoTestResultRow: View {
    let result: UserInfoTestResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(result.testName)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: result.isConsistent ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.isConsistent ? .green : .red)
            }
            
            Text(result.description)
                .font(.subheadline)
                .foregroundColor(result.isConsistent ? .green : .red)
            
            if !result.isConsistent {
                VStack(alignment: .leading, spacing: 4) {
                    Text("个人中心: \(result.profileValue)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("订阅页面: \(result.subscriptionValue)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

// MARK: - Preview

#Preview {
    UserInfoConsistencyTest()
}
