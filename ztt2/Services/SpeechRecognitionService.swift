//
//  SpeechRecognitionService.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//  语音识别服务 - 支持实时语音转文字和自动标点符号
//

import Foundation
import Speech
import AVFoundation

/**
 * 语音识别错误类型
 */
enum SpeechRecognitionError: Error, LocalizedError {
    case speechRecognitionNotAuthorized
    case microphoneNotAuthorized
    case speechRecognizerNotAvailable
    case unableToCreateRequest
    case recognitionFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .speechRecognitionNotAuthorized:
            return "语音识别权限未授权"
        case .microphoneNotAuthorized:
            return "麦克风权限未授权"
        case .speechRecognizerNotAvailable:
            return "语音识别服务不可用"
        case .unableToCreateRequest:
            return "无法创建语音识别请求"
        case .recognitionFailed(let message):
            return "语音识别失败: \(message)"
        }
    }
}

/**
 * 语音识别服务
 * 提供实时语音转文字功能，支持自动标点符号
 */
@MainActor
class SpeechRecognitionService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var recognizedText = ""
    @Published var authorizationStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    @Published var microphonePermissionStatus: AVAudioSession.RecordPermission = .undetermined
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()

    // 累积文本 - 保持已转换的内容
    private var accumulatedText: String = ""
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupSpeechRecognizer()
        checkPermissions()
    }
    
    // MARK: - Setup
    private func setupSpeechRecognizer() {
        // 设置中文语音识别器，支持自动标点符号
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self
    }
    
    // MARK: - Permission Management
    func checkPermissions() {
        // 检查语音识别权限
        authorizationStatus = SFSpeechRecognizer.authorizationStatus()
        
        // 检查麦克风权限
        microphonePermissionStatus = AVAudioSession.sharedInstance().recordPermission
    }
    
    func requestPermissions() async {
        // 请求语音识别权限
        await requestSpeechRecognitionPermission()
        
        // 请求麦克风权限
        await requestMicrophonePermission()
    }
    
    private func requestSpeechRecognitionPermission() async {
        await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                DispatchQueue.main.async {
                    self.authorizationStatus = status
                    continuation.resume()
                }
            }
        }
    }
    
    private func requestMicrophonePermission() async {
        await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.microphonePermissionStatus = granted ? .granted : .denied
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Recording Control
    func startRecording() async throws {
        // 检查权限
        guard authorizationStatus == .authorized else {
            throw SpeechRecognitionError.speechRecognitionNotAuthorized
        }
        
        guard microphonePermissionStatus == .granted else {
            throw SpeechRecognitionError.microphoneNotAuthorized
        }
        
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            throw SpeechRecognitionError.speechRecognizerNotAvailable
        }
        
        // 停止之前的识别任务
        stopRecording()
        
        // 配置音频会话
        try configureAudioSession()
        
        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw SpeechRecognitionError.unableToCreateRequest
        }
        
        // 启用实时结果和自动标点符号（iOS 16+支持）
        recognitionRequest.shouldReportPartialResults = true
        if #available(iOS 16.0, *) {
            recognitionRequest.addsPunctuation = true
        }
        
        // 获取音频输入节点
        let inputNode = audioEngine.inputNode
        
        // 创建识别任务
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if let result = result {
                    let newText = result.bestTranscription.formattedString

                    // 将累积文本与当前识别结果组合
                    self.recognizedText = self.accumulatedText + newText

                    // 如果识别任务完成，将当前结果添加到累积文本中
                    if result.isFinal {
                        // 添加当前识别结果到累积文本，并在末尾添加空格
                        self.accumulatedText += newText
                        if !newText.isEmpty && !self.accumulatedText.hasSuffix(" ") {
                            self.accumulatedText += " "
                        }
                        return
                    }
                }

                if let error = error {
                    self.handleRecognitionError(error)
                }
            }
        }
        
        // 配置音频格式
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        // 启动音频引擎
        audioEngine.prepare()
        try audioEngine.start()
        
        isRecording = true
        // 不清空recognizedText，保持累积的内容
        errorMessage = nil
    }
    
    func stopRecording() {
        // 停止音频引擎
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }
        
        // 结束识别请求
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        
        // 完成识别任务
        recognitionTask?.finish()
        recognitionTask = nil
        
        isRecording = false
    }
    
    // MARK: - Audio Session Configuration
    private func configureAudioSession() throws {
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }
    
    // MARK: - Error Handling
    private func handleRecognitionError(_ error: Error) {
        stopRecording()
        
        let nsError = error as NSError
        
        // 根据错误类型提供更友好的错误信息
        switch nsError.code {
        case 203: // No speech detected
            // 不显示错误，这是正常情况
            return
        case 216: // Recognition task finished
            // 不显示错误，这是正常完成
            return
        case 1700: // Audio session error
            errorMessage = "音频设备忙碌，请稍后再试"
        case 1101: // Network error
            errorMessage = "网络连接问题，请检查网络后重试"
        default:
            // 只有在真正的错误情况下才显示错误信息
            if nsError.localizedDescription.contains("No speech detected") {
                return
            }
            errorMessage = "语音识别遇到问题，请重试"
        }
    }
    
    func clearError() {
        errorMessage = nil
    }

    /**
     * 清空累积的识别文本
     * 用于重新开始录音或清空内容
     */
    func clearAccumulatedText() {
        accumulatedText = ""
        recognizedText = ""
    }
    
    // MARK: - Utility Methods
    var canStartRecording: Bool {
        return authorizationStatus == .authorized &&
               microphonePermissionStatus == .granted &&
               speechRecognizer?.isAvailable == true &&
               !isRecording
    }
    
    var permissionStatusMessage: String {
        if authorizationStatus != .authorized {
            return "需要语音识别权限"
        }
        if microphonePermissionStatus != .granted {
            return "需要麦克风权限"
        }
        if speechRecognizer?.isAvailable != true {
            return "语音识别不可用"
        }
        return ""
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionService: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        // 语音识别可用性变化时的处理
        Task { @MainActor in
            if !available && isRecording {
                stopRecording()
                errorMessage = "语音识别服务暂时不可用"
            }
        }
    }
}
