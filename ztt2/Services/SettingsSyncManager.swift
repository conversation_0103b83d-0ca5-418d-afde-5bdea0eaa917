//
//  SettingsSyncManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import Combine

/**
 * 设置同步管理器
 * 使用NSUbiquitousKeyValueStore实现跨设备设置同步
 */
@MainActor
class SettingsSyncManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = SettingsSyncManager()
    
    // MARK: - Private Properties
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Setting Keys
    private enum SettingKeys {
        static let appLanguage = "app_language"
        static let notificationsEnabled = "notifications_enabled"
        static let soundEnabled = "sound_enabled"
        static let hapticFeedbackEnabled = "haptic_feedback_enabled"
        static let autoSyncEnabled = "auto_sync_enabled"
        static let lastSyncTimestamp = "last_sync_timestamp"
    }
    
    // MARK: - Published Properties
    @Published var appLanguage: String = "zh-Hans" {
        didSet {
            syncSetting(key: SettingKeys.appLanguage, value: appLanguage)
        }
    }
    
    @Published var notificationsEnabled: Bool = true {
        didSet {
            syncSetting(key: SettingKeys.notificationsEnabled, value: notificationsEnabled)
        }
    }
    
    @Published var soundEnabled: Bool = true {
        didSet {
            syncSetting(key: SettingKeys.soundEnabled, value: soundEnabled)
        }
    }
    
    @Published var hapticFeedbackEnabled: Bool = true {
        didSet {
            syncSetting(key: SettingKeys.hapticFeedbackEnabled, value: hapticFeedbackEnabled)
        }
    }
    
    @Published var autoSyncEnabled: Bool = true {
        didSet {
            syncSetting(key: SettingKeys.autoSyncEnabled, value: autoSyncEnabled)
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        setupUbiquitousStoreObserver()
        loadSettingsFromUbiquitousStore()
        print("☁️ 设置同步管理器初始化完成")
    }
    
    // MARK: - Setup Methods
    
    /**
     * 设置NSUbiquitousKeyValueStore观察者
     */
    private func setupUbiquitousStoreObserver() {
        NotificationCenter.default.addObserver(
            forName: NSUbiquitousKeyValueStore.didChangeExternallyNotification,
            object: ubiquitousStore,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleExternalChange(notification)
            }
        }
    }
    
    /**
     * 从iCloud加载设置
     */
    private func loadSettingsFromUbiquitousStore() {
        // 同步iCloud数据到本地
        ubiquitousStore.synchronize()
        
        // 加载各项设置
        if let language = ubiquitousStore.string(forKey: SettingKeys.appLanguage) {
            appLanguage = language
        }
        
        notificationsEnabled = ubiquitousStore.bool(forKey: SettingKeys.notificationsEnabled)
        soundEnabled = ubiquitousStore.bool(forKey: SettingKeys.soundEnabled)
        hapticFeedbackEnabled = ubiquitousStore.bool(forKey: SettingKeys.hapticFeedbackEnabled)
        autoSyncEnabled = ubiquitousStore.bool(forKey: SettingKeys.autoSyncEnabled)
        
        print("📱 从iCloud加载设置完成")
    }
    
    // MARK: - Sync Methods
    
    /**
     * 同步设置到iCloud
     */
    private func syncSetting<T>(key: String, value: T) {
        switch value {
        case let stringValue as String:
            ubiquitousStore.set(stringValue, forKey: key)
        case let boolValue as Bool:
            ubiquitousStore.set(boolValue, forKey: key)
        case let intValue as Int:
            ubiquitousStore.set(intValue, forKey: key)
        case let doubleValue as Double:
            ubiquitousStore.set(doubleValue, forKey: key)
        default:
            print("⚠️ 不支持的设置类型: \(type(of: value))")
            return
        }
        
        // 立即同步到iCloud
        ubiquitousStore.synchronize()
        
        // 更新最后同步时间
        ubiquitousStore.set(Date().timeIntervalSince1970, forKey: SettingKeys.lastSyncTimestamp)
        
        print("☁️ 设置已同步到iCloud: \(key) = \(value)")
    }
    
    /**
     * 处理外部变更（来自其他设备）
     */
    private func handleExternalChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reason = userInfo[NSUbiquitousKeyValueStoreChangeReasonKey] as? Int else {
            return
        }
        
        print("📥 检测到来自其他设备的设置变更")
        
        switch reason {
        case NSUbiquitousKeyValueStoreServerChange:
            print("📡 服务器端设置变更")
            loadSettingsFromUbiquitousStore()
            
        case NSUbiquitousKeyValueStoreInitialSyncChange:
            print("🔄 初始同步设置变更")
            loadSettingsFromUbiquitousStore()
            
        case NSUbiquitousKeyValueStoreQuotaViolationChange:
            print("⚠️ iCloud存储配额超限")
            
        case NSUbiquitousKeyValueStoreAccountChange:
            print("👤 iCloud账户变更")
            loadSettingsFromUbiquitousStore()
            
        default:
            print("❓ 未知的设置变更原因: \(reason)")
        }
    }
    
    // MARK: - Public Methods
    
    /**
     * 手动触发设置同步
     */
    func triggerManualSync() {
        print("🔄 手动触发设置同步...")
        
        // 同步所有当前设置到iCloud
        syncSetting(key: SettingKeys.appLanguage, value: appLanguage)
        syncSetting(key: SettingKeys.notificationsEnabled, value: notificationsEnabled)
        syncSetting(key: SettingKeys.soundEnabled, value: soundEnabled)
        syncSetting(key: SettingKeys.hapticFeedbackEnabled, value: hapticFeedbackEnabled)
        syncSetting(key: SettingKeys.autoSyncEnabled, value: autoSyncEnabled)
        
        // 强制同步
        ubiquitousStore.synchronize()
        
        print("✅ 手动设置同步完成")
    }
    
    /**
     * 重置所有设置为默认值
     */
    func resetToDefaults() {
        print("🔄 重置设置为默认值...")
        
        appLanguage = "zh-Hans"
        notificationsEnabled = true
        soundEnabled = true
        hapticFeedbackEnabled = true
        autoSyncEnabled = true
        
        print("✅ 设置已重置为默认值")
    }
    
    /**
     * 获取最后同步时间
     */
    func getLastSyncTime() -> Date? {
        let timestamp = ubiquitousStore.double(forKey: SettingKeys.lastSyncTimestamp)
        return timestamp > 0 ? Date(timeIntervalSince1970: timestamp) : nil
    }
    
    /**
     * 检查iCloud设置同步是否可用
     */
    func isUbiquitousStoreAvailable() -> Bool {
        // 检查是否有iCloud账户
        if let _ = FileManager.default.ubiquityIdentityToken {
            return true
        } else {
            print("⚠️ 未检测到iCloud账户，设置同步不可用")
            return false
        }
    }
    
    // MARK: - Convenience Methods
    
    /**
     * 应用语言设置
     */
    func applyLanguageSetting() {
        // 这里可以添加语言切换逻辑
        print("🌐 应用语言设置: \(appLanguage)")
    }
    
    /**
     * 应用通知设置
     */
    func applyNotificationSetting() {
        // 这里可以添加通知权限请求逻辑
        print("🔔 应用通知设置: \(notificationsEnabled)")
    }
    
    /**
     * 应用声音设置
     */
    func applySoundSetting() {
        // 这里可以添加声音设置逻辑
        print("🔊 应用声音设置: \(soundEnabled)")
    }
    
    /**
     * 应用触觉反馈设置
     */
    func applyHapticFeedbackSetting() {
        // 这里可以添加触觉反馈设置逻辑
        print("📳 应用触觉反馈设置: \(hapticFeedbackEnabled)")
    }
}
