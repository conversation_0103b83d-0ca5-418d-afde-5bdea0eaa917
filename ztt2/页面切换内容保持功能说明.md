# 页面切换内容保持功能说明

## 🔧 修复的问题

### 问题描述
在测试中发现，当用户完成了一部分语音转换内容后，切换到首页或个人中心页面，再切换回到成长日记页面时，已转换的内容会消失。这会导致如果用户误触TabBar切换页面后，再返回成长日记页面时需要重新录入所有内容。

### 问题影响
- **用户体验差**：误触切换页面后内容丢失，用户需要重新录入
- **工作效率低**：长篇内容录入过程中的意外切换会造成大量重复工作
- **使用焦虑**：用户担心内容丢失，不敢随意操作其他功能

## ✅ 解决方案

### 核心思路
采用**本地缓存机制**，将用户输入的内容实时保存到UserDefaults中，在页面重新加载时自动恢复内容。

### 技术实现

#### 1. 添加内容变化监听
```swift
.onChange(of: speechRecognitionService.recognizedText) { recognizedText in
    // 当语音识别文本变化时，更新日记内容
    if !recognizedText.isEmpty {
        diaryContent = recognizedText
        // 缓存内容到UserDefaults
        saveDraftContent()
    }
}
.onChange(of: diaryContent) { content in
    // 当用户手动编辑内容时也进行缓存
    saveDraftContent()
}
.onChange(of: diaryTitle) { title in
    // 当用户编辑标题时也进行缓存
    saveDraftContent()
}
```

#### 2. 实现草稿保存功能
```swift
private func saveDraftContent() {
    let draftData: [String: Any] = [
        "title": diaryTitle,
        "content": diaryContent,
        "timestamp": selectedDate.timeIntervalSince1970,
        "selectedMemberId": selectedMember?.objectID.uriRepresentation().absoluteString ?? ""
    ]
    UserDefaults.standard.set(draftData, forKey: "GrowthDiaryDraft")
}
```

#### 3. 实现草稿加载功能
```swift
private func loadDraftContent() {
    guard let draftData = UserDefaults.standard.dictionary(forKey: "GrowthDiaryDraft") else {
        return
    }
    
    // 恢复标题和内容
    if let title = draftData["title"] as? String {
        diaryTitle = title
    }
    
    if let content = draftData["content"] as? String {
        diaryContent = content
        // 同步到语音识别服务
        speechRecognitionService.recognizedText = content
    }
    
    // 恢复日期和选中成员
    // ...
}
```

#### 4. 页面加载时恢复内容
```swift
.onAppear {
    // ... 其他初始化代码
    
    // 恢复缓存的内容
    loadDraftContent()
}
```

#### 5. 保存成功后清空缓存
```swift
if success {
    // 清空输入框和重置选择
    diaryTitle = ""
    diaryContent = ""
    selectedMember = nil
    selectedDate = Date()
    
    // 清空语音识别累积文本
    speechRecognitionService.clearAccumulatedText()
    
    // 清空草稿缓存
    clearDraftContent()
}
```

## 🎯 修复后的用户体验

### 使用场景示例

#### 场景1：语音录入过程中误触切换
1. 用户在成长日记页面录入了3句话："今天天气很好。我们去公园玩了。孩子很开心。"
2. 用户误触TabBar切换到首页
3. 用户立即切换回成长日记页面
4. **结果**：之前录入的内容完整保留，可以继续录入

#### 场景2：录入过程中查看其他信息
1. 用户录入了一半内容，想要查看个人中心的某些信息
2. 切换到个人中心页面查看
3. 查看完毕后返回成长日记页面
4. **结果**：录入内容完整保留，包括标题、内容、日期、选中成员等

#### 场景3：应用被系统回收后恢复
1. 用户录入内容后，应用被系统回收到后台
2. 用户重新打开应用
3. 进入成长日记页面
4. **结果**：之前的草稿内容自动恢复

### 缓存的内容包括
- **日记标题**：用户输入的标题文字
- **日记内容**：语音转换+手动编辑的所有内容
- **选择日期**：用户选择的记录日期
- **选中成员**：用户选择的记录对象

## 🔄 缓存生命周期

### 自动保存时机
- 语音识别文本发生变化时
- 用户手动编辑标题时
- 用户手动编辑内容时
- 用户选择日期时
- 用户选择成员时

### 自动清空时机
- 日记保存成功后
- 用户手动清空内容时

### 手动管理
- 提供`clearDraftContent()`方法供手动清空
- 可以根据需要扩展更多管理功能

## 🧪 测试覆盖

### 功能测试
- ✅ 草稿保存功能测试
- ✅ 草稿加载功能测试  
- ✅ 草稿清空功能测试
- ✅ 完整工作流程测试

### 边界情况测试
- ✅ 空内容草稿保存
- ✅ 长文本内容保存
- ✅ 特殊字符内容保存

### 性能测试
- ✅ 草稿保存性能测试
- ✅ 草稿加载性能测试

## 📱 技术特点

### 优势
- **实时保存**：内容变化时立即保存，不会丢失
- **自动恢复**：页面重新加载时自动恢复内容
- **轻量级**：使用UserDefaults，性能开销小
- **可靠性**：即使应用被系统回收也能恢复
- **透明性**：对用户完全透明，无需额外操作

### 存储策略
- **存储位置**：UserDefaults（本地存储）
- **存储格式**：Dictionary格式，便于扩展
- **存储时机**：内容变化时实时保存
- **清理策略**：保存成功后自动清理

## 🎉 总结

通过实现本地草稿缓存机制，完全解决了页面切换导致内容丢失的问题：

1. **用户体验提升**：不再担心误触切换页面导致内容丢失
2. **工作效率提高**：可以安心在录入过程中查看其他页面
3. **功能更可靠**：即使应用被系统回收也能恢复内容
4. **操作更自然**：无需额外的保存操作，一切都是自动的

现在用户可以放心地在成长日记页面进行语音录入，即使意外切换页面也不会丢失任何内容！
