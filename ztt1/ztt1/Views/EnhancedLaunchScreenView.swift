//
//  EnhancedLaunchScreenView.swift
//  ztt1
//
//  Created by AI Assistant on 2025/8/4.
//

import SwiftUI

/**
 * 增强版启动页视图 - 全新设计
 * 与应用风格保持一致，增加更多创意动画元素
 */
struct EnhancedLaunchScreenView: View {
    // MARK: - Animation States
    @State private var logoScale: CGFloat = 0.3
    @State private var logoOpacity: Double = 0.0
    @State private var logoRotation: Double = 0.0
    @State private var titleScale: CGFloat = 0.8
    @State private var titleOpacity: Double = 0.0
    @State private var subtitleOffset: CGFloat = 30
    @State private var subtitleOpacity: Double = 0.0
    @State private var progressOpacity: Double = 0.0
    @State private var particlesOpacity: Double = 0.0
    @State private var backgroundScale: CGFloat = 1.3
    @State private var backgroundRotation: Double = 0.0
    @State private var rippleScale: CGFloat = 0.0
    @State private var rippleOpacity: Double = 0.8
    @State private var sparkleOpacity: Double = 0.0
    @State private var waveOffset: CGFloat = 0.0
    @State private var breathingScale: CGFloat = 1.0
    @State private var pulseScale: CGFloat = 1.0

    var body: some View {
        ZStack {
            // 动态背景层
            createBackgroundLayer()
            
            // 装饰元素层
            createDecorationLayer()
            
            // 主要内容层
            createMainContentLayer()
        }
        .onAppear {
            startEnhancedAnimationSequence()
        }
    }
    
    // MARK: - Background Layer
    private func createBackgroundLayer() -> some View {
        ZStack {
            // 主背景渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#E8F9C5").opacity(0.9),
                    Color(hex: "#f8fdf0"),
                    Color(hex: "#B5E36B").opacity(0.4),
                    Color(hex: "#fcfff4")
                ]),
                center: .center,
                startRadius: 50,
                endRadius: 500
            )
            .scaleEffect(backgroundScale)
            .rotationEffect(.degrees(backgroundRotation))
            .ignoresSafeArea()
            
            // 动态波纹效果
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#B5E36B").opacity(0.3),
                                Color(hex: "#FFE49E").opacity(0.2),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
                    .frame(width: 200 + CGFloat(index * 100), height: 200 + CGFloat(index * 100))
                    .scaleEffect(rippleScale)
                    .opacity(rippleOpacity * (1.0 - Double(index) * 0.3))
                    .animation(
                        .easeInOut(duration: 2.0 + Double(index) * 0.5)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.4),
                        value: rippleScale
                    )
            }
        }
    }
    
    // MARK: - Decoration Layer
    private func createDecorationLayer() -> some View {
        ZStack {
            // 浮动装饰元素
            GeometryReader { geometry in
                ForEach(0..<8, id: \.self) { index in
                    EnhancedFloatingElement(
                        index: index,
                        geometry: geometry,
                        opacity: particlesOpacity
                    )
                }
            }
            
            // 闪烁星星效果
            ForEach(0..<12, id: \.self) { index in
                createSparkleElement(index: index)
            }
        }
    }
    
    // MARK: - Main Content Layer
    private func createMainContentLayer() -> some View {
        VStack(spacing: 0) {
            Spacer()
            
            // Logo和标题区域
            VStack(spacing: 50) {
                // 增强的Logo区域
                createEnhancedLogoSection()
                
                // 标题和副标题
                createTitleSection()
            }
            
            Spacer()
            
            // 底部加载区域
            createLoadingSection()
        }
    }
    
    // MARK: - Enhanced Logo Section
    private func createEnhancedLogoSection() -> some View {
        ZStack {
            // 最外层呼吸光环
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#B5E36B").opacity(0.3),
                            Color(hex: "#FFE49E").opacity(0.2),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 150
                    )
                )
                .frame(width: 300, height: 300)
                .scaleEffect(breathingScale)
                .opacity(logoOpacity * 0.6)
            
            // 中层旋转光环
            Circle()
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#B5E36B").opacity(0.8),
                            Color(hex: "#FFE49E").opacity(0.6),
                            Color(hex: "#87C441").opacity(0.8),
                            Color(hex: "#B5E36B").opacity(0.8)
                        ]),
                        center: .center
                    ),
                    lineWidth: 3
                )
                .frame(width: 200, height: 200)
                .rotationEffect(.degrees(logoRotation))
                .opacity(logoOpacity * 0.8)
            
            // Logo背景圆
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.98),
                            Color(hex: "#E8F9C5").opacity(0.95)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 160, height: 160)
                .shadow(color: Color(hex: "#B5E36B").opacity(0.4), radius: 25, x: 0, y: 15)
                .scaleEffect(logoScale * pulseScale)
            
            // Logo图片
            Image("logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 100, height: 100)
                .scaleEffect(logoScale)
                .rotationEffect(.degrees(logoRotation * 0.1))
        }
        .opacity(logoOpacity)
    }
    
    // MARK: - Title Section
    private func createTitleSection() -> some View {
        VStack(spacing: 20) {
            // 主标题
            Text("转团团")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#87C441"),
                            Color(hex: "#B5E36B")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .scaleEffect(titleScale)
                .opacity(titleOpacity)
            
            // 副标题
            Text("让成长更有意义")
                .font(.system(size: 20, weight: .medium, design: .rounded))
                .foregroundColor(Color(hex: "#999999"))
                .opacity(subtitleOpacity)
                .offset(y: subtitleOffset)
        }
    }
    
    // MARK: - Loading Section
    private func createLoadingSection() -> some View {
        VStack(spacing: 30) {
            // 创意加载指示器
            createCreativeLoadingIndicator()
            
            // 初始化状态提示
            Text("正在初始化应用...")
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundColor(Color(hex: "#999999"))
                .opacity(progressOpacity)
        }
        .padding(.bottom, 80)
    }
    
    // MARK: - Creative Loading Indicator
    private func createCreativeLoadingIndicator() -> some View {
        HStack(spacing: 8) {
            ForEach(0..<5) { index in
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#B5E36B"),
                                Color(hex: "#FFE49E"),
                                Color(hex: "#87C441")
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 12, height: 12)
                    .scaleEffect(logoScale)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.15),
                        value: logoScale
                    )
            }
        }
        .opacity(progressOpacity)
    }

    // MARK: - Sparkle Element
    private func createSparkleElement(index: Int) -> some View {
        let positions: [(CGFloat, CGFloat)] = [
            (0.1, 0.15), (0.9, 0.2), (0.15, 0.8), (0.85, 0.75),
            (0.3, 0.3), (0.7, 0.4), (0.2, 0.6), (0.8, 0.65),
            (0.05, 0.45), (0.95, 0.55), (0.4, 0.1), (0.6, 0.9)
        ]

        let position = positions[index % positions.count]

        return GeometryReader { geometry in
            Image(systemName: "sparkle")
                .font(.system(size: CGFloat.random(in: 12...20), weight: .light))
                .foregroundColor(Color(hex: "#FFE49E").opacity(0.8))
                .opacity(sparkleOpacity)
                .position(
                    x: geometry.size.width * position.0,
                    y: geometry.size.height * position.1
                )
                .animation(
                    .easeInOut(duration: Double.random(in: 1.5...3.0))
                    .repeatForever(autoreverses: true)
                    .delay(Double(index) * 0.2),
                    value: sparkleOpacity
                )
        }
    }

    // MARK: - Enhanced Animation Sequence
    private func startEnhancedAnimationSequence() {
        // 背景动画
        withAnimation(.easeInOut(duration: 2.5)) {
            backgroundScale = 1.0
        }

        withAnimation(.linear(duration: 20.0).repeatForever(autoreverses: false)) {
            backgroundRotation = 360
        }

        // 波纹效果
        withAnimation(.easeOut(duration: 1.5).delay(0.3)) {
            rippleScale = 1.0
        }

        // Logo出现动画
        withAnimation(.spring(response: 1.5, dampingFraction: 0.7).delay(0.5)) {
            logoScale = 1.0
            logoOpacity = 1.0
        }

        // Logo旋转动画
        withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false).delay(1.0)) {
            logoRotation = 360
        }

        // 呼吸效果
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true).delay(1.2)) {
            breathingScale = 1.1
        }

        // 脉冲效果
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true).delay(1.5)) {
            pulseScale = 1.05
        }

        // 标题出现动画
        withAnimation(.spring(response: 1.0, dampingFraction: 0.8).delay(1.0)) {
            titleScale = 1.0
            titleOpacity = 1.0
        }

        // 副标题出现动画
        withAnimation(.easeOut(duration: 0.8).delay(1.3)) {
            subtitleOffset = 0
            subtitleOpacity = 1.0
        }

        // 装饰元素出现动画
        withAnimation(.easeOut(duration: 1.2).delay(0.8)) {
            particlesOpacity = 1.0
        }

        // 星星闪烁动画
        withAnimation(.easeOut(duration: 1.0).delay(1.5)) {
            sparkleOpacity = 1.0
        }

        // 加载指示器出现动画
        withAnimation(.easeOut(duration: 0.8).delay(2.0)) {
            progressOpacity = 1.0
        }
    }
}

// MARK: - Enhanced Floating Element

/**
 * 增强版浮动装饰元素
 */
struct EnhancedFloatingElement: View {
    let index: Int
    let geometry: GeometryProxy
    let opacity: Double

    @State private var offset: CGSize = .zero
    @State private var rotation: Double = 0
    @State private var scale: CGFloat = 1.0
    @State private var colorIndex: Int = 0

    private var educationSymbols: [String] {
        ["📚", "✏️", "🎯", "⭐", "🏆", "🎨", "🌟", "💡"]
    }

    private var colors: [Color] {
        [
            Color(hex: "#B5E36B").opacity(0.7),
            Color(hex: "#FFE49E").opacity(0.7),
            Color(hex: "#87C441").opacity(0.7),
            Color(hex: "#E8F9C5").opacity(0.9),
            Color.white.opacity(0.8),
            Color(hex: "#B5E36B").opacity(0.5),
            Color(hex: "#FFE49E").opacity(0.5),
            Color(hex: "#87C441").opacity(0.6)
        ]
    }

    var body: some View {
        ZStack {
            // 背景圆形
            Circle()
                .fill(colors[colorIndex % colors.count])
                .frame(width: elementSize, height: elementSize)
                .blur(radius: 1.5)

            // 教育符号
            Text(educationSymbols[index % educationSymbols.count])
                .font(.system(size: elementSize * 0.5))
                .rotationEffect(.degrees(rotation))
        }
        .scaleEffect(scale)
        .offset(offset)
        .opacity(opacity)
        .position(initialPosition)
        .onAppear {
            startEnhancedFloatingAnimation()
        }
    }

    private var elementSize: CGFloat {
        let sizes: [CGFloat] = [35, 45, 30, 40, 38, 42, 32, 48]
        return sizes[index % sizes.count]
    }

    private var initialPosition: CGPoint {
        let positions: [(CGFloat, CGFloat)] = [
            (0.12, 0.18), (0.88, 0.22), (0.08, 0.72), (0.92, 0.78),
            (0.18, 0.42), (0.82, 0.58), (0.25, 0.65), (0.75, 0.35)
        ]
        let pos = positions[index % positions.count]
        return CGPoint(
            x: geometry.size.width * pos.0,
            y: geometry.size.height * pos.1
        )
    }

    private func startEnhancedFloatingAnimation() {
        let duration = Double.random(in: 3.0...7.0)
        let delay = Double(index) * 0.25

        // 浮动动画
        withAnimation(
            .easeInOut(duration: duration)
            .repeatForever(autoreverses: true)
            .delay(delay)
        ) {
            offset = CGSize(
                width: Double.random(in: -40...40),
                height: Double.random(in: -50...50)
            )
        }

        // 旋转动画
        withAnimation(
            .linear(duration: duration * 1.5)
            .repeatForever(autoreverses: false)
            .delay(delay)
        ) {
            rotation = 360
        }

        // 缩放动画
        withAnimation(
            .easeInOut(duration: duration * 0.7)
            .repeatForever(autoreverses: true)
            .delay(delay + 0.3)
        ) {
            scale = Double.random(in: 0.7...1.3)
        }

        // 颜色变化动画
        withAnimation(
            .easeInOut(duration: duration * 2)
            .repeatForever(autoreverses: false)
            .delay(delay + 1.0)
        ) {
            colorIndex = Int.random(in: 0..<colors.count)
        }
    }
}
