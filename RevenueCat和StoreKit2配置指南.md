# RevenueCat + StoreKit2 配置指南

## 概述
本指南将帮助您完成ztt2项目的RevenueCat和StoreKit2集成配置，包括App Store Connect设置、RevenueCat Dashboard配置和代码集成。

## 第一步：App Store Connect 配置

### 1.1 创建App Store Connect应用
1. 登录 [App Store Connect](https://appstoreconnect.apple.com/)
2. 点击"我的App" → "+"号 → "新建App"
3. 填写应用信息：
   - 平台：iOS
   - 名称：转团团
   - 主要语言：简体中文
   - Bundle ID：com.yourcompany.ztt2（请替换为您的实际Bundle ID）
   - SKU：ztt2-ios

### 1.2 创建订阅产品
在App Store Connect中创建以下订阅产品：

#### 初级会员订阅
1. 进入您的应用 → "App内购买项目" → "+"号 → "自动续期订阅"
2. 产品ID：`com.ztt2.subscription.monthly.basic`
3. 参考名称：初级会员月度订阅
4. 订阅群组：创建新群组"ztt2_subscriptions"
5. 价格：¥38/月

6. 产品ID：`com.ztt2.subscription.yearly.basic`
7. 参考名称：初级会员年度订阅
8. 订阅群组：选择"ztt2_subscriptions"
9. 价格：¥188/年

#### 高级会员订阅
1. 产品ID：`com.ztt2.subscription.monthly.premium`
2. 参考名称：高级会员月度订阅
3. 订阅群组：选择"ztt2_subscriptions"
4. 价格：¥58/月

5. 产品ID：`com.ztt2.subscription.yearly.premium`
6. 参考名称：高级会员年度订阅
7. 订阅群组：选择"ztt2_subscriptions"
8. 价格：¥388/年

### 1.3 配置订阅详情
为每个订阅产品配置：
- 显示名称（中文）
- 描述（详细说明功能权益）
- 审核信息
- 本地化信息

## 第二步：RevenueCat Dashboard 配置

### 2.1 创建RevenueCat项目
1. 访问 [RevenueCat Dashboard](https://app.revenuecat.com/)
2. 注册账号并登录
3. 点击"Create new project"
4. 项目名称：ztt2
5. 选择平台：iOS

### 2.2 配置App Store Connect集成
1. 在RevenueCat项目中，进入"App settings"
2. 点击"Apple App Store"
3. 输入您的Bundle ID：com.yourcompany.ztt2
4. 上传App Store Connect API密钥：
   - 在App Store Connect中生成API密钥
   - 下载.p8文件并上传到RevenueCat

### 2.3 创建产品和权限
在RevenueCat Dashboard中：

#### 创建权限（Entitlements）
1. 进入"Entitlements"页面
2. 创建以下权限：
   - `basic_member`：初级会员权限
   - `premium_member`：高级会员权限

#### 配置产品
1. 进入"Products"页面
2. 添加以下产品（与App Store Connect中的产品ID一致）：
   - `com.ztt2.subscription.monthly.basic`
   - `com.ztt2.subscription.yearly.basic`
   - `com.ztt2.subscription.monthly.premium`
   - `com.ztt2.subscription.yearly.premium`

#### 创建Offering
1. 进入"Offerings"页面
2. 创建默认Offering
3. 添加Package：
   - Monthly Basic：关联到basic_member权限
   - Yearly Basic：关联到basic_member权限
   - Monthly Premium：关联到premium_member权限
   - Yearly Premium：关联到premium_member权限

### 2.4 获取API密钥
1. 在RevenueCat Dashboard中，进入"API keys"
2. 复制"Apple App Store"的Public API Key
3. 这个密钥需要在代码中使用

## 第三步：代码配置

### 3.1 更新RevenueCat API Key
在 `ztt2App.swift` 文件中，将API Key替换为真实的密钥：

```swift
// 将此行：
let apiKey = "appl_YOUR_REVENUECAT_API_KEY_HERE"

// 替换为：
let apiKey = "appl_您从RevenueCat获取的真实API密钥"
```

### 3.2 验证产品ID
确保代码中的产品ID与App Store Connect中的产品ID完全一致：

```swift
enum ProductIDs {
    static let basicMonthly = "com.ztt2.subscription.monthly.basic"
    static let basicYearly = "com.ztt2.subscription.yearly.basic"
    static let premiumMonthly = "com.ztt2.subscription.monthly.premium"
    static let premiumYearly = "com.ztt2.subscription.yearly.premium"
}
```

### 3.3 配置权限ID
确保权限ID与RevenueCat Dashboard中的权限一致：

```swift
enum EntitlementIDs {
    static let basicMember = "basic_member"
    static let premiumMember = "premium_member"
}
```

## 第四步：测试配置

### 4.1 沙盒测试
1. 在App Store Connect中创建沙盒测试用户
2. 在iOS设备上登录沙盒账户
3. 运行应用并测试购买流程

### 4.2 验证RevenueCat集成
1. 在RevenueCat Dashboard中查看"Customer"页面
2. 测试购买后，应该能看到用户和订阅信息
3. 验证权限是否正确分配

## 第五步：权限功能映射

### 免费用户
- 创建家庭成员（最多10个）
- 积分管理
- 兑换奖品功能
- 成长日记功能

### 初级会员（basic_member）
- 免费用户所有功能
- 解锁大转盘道具
- 支持多设备同步
- 最多20个家庭成员

### 高级会员（premium_member）
- 初级会员所有功能
- 解锁盲盒、刮刮卡
- 解锁AI分析功能
- 最多50个家庭成员

## 注意事项

1. **Bundle ID一致性**：确保Xcode项目、App Store Connect和RevenueCat中的Bundle ID完全一致
2. **产品ID格式**：建议使用反向域名格式，如`com.yourcompany.ztt2.subscription.monthly.basic`
3. **测试环境**：开发阶段使用沙盒环境，发布前切换到生产环境
4. **权限检查**：在应用中正确实现权限检查逻辑
5. **错误处理**：实现完善的错误处理和用户提示

## 常见问题

### Q: 购买失败，提示"产品不可用"
A: 检查产品ID是否正确，确保在App Store Connect中产品状态为"准备提交"

### Q: RevenueCat显示"Invalid API Key"
A: 确认API Key是否正确复制，检查是否使用了正确的平台密钥

### Q: 权限检查不生效
A: 验证RevenueCat Dashboard中的权限配置，确保产品正确关联到权限

### Q: 沙盒测试无法购买
A: 确认使用沙盒测试账户，检查设备是否正确登录沙盒账户

## 技术支持

如果遇到问题，可以参考：
- [RevenueCat官方文档](https://docs.revenuecat.com/)
- [Apple StoreKit文档](https://developer.apple.com/documentation/storekit)
- [App Store Connect帮助](https://help.apple.com/app-store-connect/)
